using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Net;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for PreMarketFilterService
/// Tests pre-market analysis, risk assessment, and trading block decisions
/// </summary>
[Trait("Category","Legacy")]
public class PreMarketFilterServiceTests : IDisposable
{
    private readonly Mock<IPolygonClientFactory> _mockPolygonFactory;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IMarketSessionGuard> _mockSessionGuard;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<PreMarketFilterService>> _mockLogger;
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly PreMarketFilterService _preMarketFilterService;

    public PreMarketFilterServiceTests()
    {
        _mockPolygonFactory = new Mock<IPolygonClientFactory>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockSessionGuard = new Mock<IMarketSessionGuard>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<PreMarketFilterService>>();
        _mockHttpClient = new Mock<HttpClient>();
        _mockDatabase = new Mock<IDatabase>();

        // Setup configuration
        var configSection = new Mock<IConfigurationSection>();
        _mockConfiguration.Setup(x => x.GetSection("PreMarketFilter")).Returns(configSection.Object);

        // Setup Redis
        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(_mockDatabase.Object);

        // Setup Polygon factory
        _mockPolygonFactory.Setup(x => x.CreateClient())
            .Returns(_mockHttpClient.Object);
        _mockPolygonFactory.Setup(x => x.AddApiKeyToUrl(It.IsAny<string>()))
            .Returns<string>(url => url + "?apikey=test");

        _preMarketFilterService = new PreMarketFilterService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            _mockSessionGuard.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Act & Assert - Constructor should not throw
        var service = new PreMarketFilterService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            _mockSessionGuard.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        service.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullPolygonFactory_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PreMarketFilterService(
            null!,
            _mockRedisService.Object,
            _mockSessionGuard.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("polygonFactory");
    }

    [Fact]
    public void Constructor_WithNullRedisService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PreMarketFilterService(
            _mockPolygonFactory.Object,
            null!,
            _mockSessionGuard.Object,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("redisService");
    }

    [Fact]
    public void Constructor_WithNullSessionGuard_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PreMarketFilterService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            null!,
            _mockConfiguration.Object,
            _mockLogger.Object);

        act.Should().Throw<ArgumentNullException>().WithParameterName("sessionGuard");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PreMarketFilterService(
            _mockPolygonFactory.Object,
            _mockRedisService.Object,
            _mockSessionGuard.Object,
            _mockConfiguration.Object,
            null!);

        act.Should().Throw<ArgumentNullException>().WithParameterName("logger");
    }

    [Fact]
    public async Task AnalyzePreMarketConditionsAsync_WithNullSymbol_ShouldThrowArgumentException()
    {
        // Act & Assert
        var act = async () => await _preMarketFilterService.AnalyzePreMarketConditionsAsync(null!);
        await act.Should().ThrowAsync<ArgumentException>().WithParameterName("symbol");
    }

    [Fact]
    public async Task AnalyzePreMarketConditionsAsync_WithEmptySymbol_ShouldThrowArgumentException()
    {
        // Act & Assert
        var act = async () => await _preMarketFilterService.AnalyzePreMarketConditionsAsync("");
        await act.Should().ThrowAsync<ArgumentException>().WithParameterName("symbol");
    }

    [Fact]
    public async Task ShouldBlockTradingAsync_WithValidSymbol_ShouldReturnBooleanResult()
    {
        // Arrange
        const string symbol = "AAPL";
        
        // Setup Redis to return no cached data
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _preMarketFilterService.ShouldBlockTradingAsync(symbol);

        // Assert
        Assert.IsType<bool>(result);
    }

    [Fact]
    public async Task GetCachedAnalysisAsync_WithNoCache_ShouldReturnNull()
    {
        // Arrange
        const string symbol = "AAPL";
        
        // Setup Redis to return no cached data
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _preMarketFilterService.GetCachedAnalysisAsync(symbol);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task AnalyzeBatchAsync_WithMultipleSymbols_ShouldReturnDictionaryResults()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        
        // Setup Redis to return no cached data
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var results = await _preMarketFilterService.AnalyzeBatchAsync(symbols);

        // Assert
        results.Should().NotBeNull();
        results.Should().HaveCount(3);
        results.Keys.Should().Contain(symbols);
    }

    [Fact]
    public async Task ClearCacheAsync_WhenCalled_ShouldNotThrow()
    {
        // Arrange
        var mockServer = new Mock<IServer>();
        var mockMultiplexer = new Mock<IConnectionMultiplexer>();
        var endpoints = new EndPoint[] { new IPEndPoint(IPAddress.Loopback, 6379) };
        
        _mockDatabase.Setup(x => x.Multiplexer).Returns(mockMultiplexer.Object);
        mockMultiplexer.Setup(x => x.GetEndPoints(It.IsAny<bool>())).Returns(endpoints);
        mockMultiplexer.Setup(x => x.GetServer(It.IsAny<EndPoint>(), It.IsAny<object>()))
            .Returns(mockServer.Object);

        // Act & Assert
        var act = async () => await _preMarketFilterService.ClearCacheAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public void Dispose_WhenCalled_ShouldNotThrow()
    {
        // Act & Assert
        var act = () => _preMarketFilterService.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public async Task AnalyzePreMarketConditionsAsync_AfterDispose_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _preMarketFilterService.Dispose();

        // Act & Assert
        var act = async () => await _preMarketFilterService.AnalyzePreMarketConditionsAsync("AAPL");
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    public void Dispose()
    {
        _preMarketFilterService?.Dispose();
    }
}
