using FluentAssertions;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for RedisKeyConstants functionality
/// </summary>
[Trait("Category", TestCategories.Unit)]
[Trait("Category","Legacy")]
public class RedisKeyConstantsTests
{
    [Fact]
    public void RedisKeyTTL_ShouldHaveCorrectValues()
    {
        // Assert - Verify all TTL constants have expected values
        RedisKeyConstants.RedisKeyTTL.Signal.Should().Be(TimeSpan.FromHours(24));
        RedisKeyConstants.RedisKeyTTL.Universe.Should().Be(TimeSpan.FromHours(24));
        RedisKeyConstants.RedisKeyTTL.Stop.Should().Be(TimeSpan.FromDays(7));
        RedisKeyConstants.RedisKeyTTL.SyntheticVix.Should().Be(TimeSpan.FromMinutes(10));
        RedisKeyConstants.RedisKeyTTL.VixData.Should().Be(TimeSpan.FromMinutes(15));
        RedisKeyConstants.RedisKeyTTL.Regime.Should().Be(TimeSpan.FromMinutes(5));
        RedisKeyConstants.RedisKeyTTL.Breadth.Should().Be(TimeSpan.FromMinutes(5));
        RedisKeyConstants.RedisKeyTTL.Execution.Should().Be(TimeSpan.FromHours(1));
        RedisKeyConstants.RedisKeyTTL.Vwap.Should().Be(TimeSpan.FromMinutes(5));
        RedisKeyConstants.RedisKeyTTL.Volatility.Should().Be(TimeSpan.FromMinutes(5));
        RedisKeyConstants.RedisKeyTTL.PreMarket.Should().Be(TimeSpan.FromHours(1));
        RedisKeyConstants.RedisKeyTTL.Throttle.Should().Be(TimeSpan.FromHours(24));
        RedisKeyConstants.RedisKeyTTL.HealthCheck.Should().Be(TimeSpan.FromMinutes(1));
        RedisKeyConstants.RedisKeyTTL.Patterns.Should().Be(TimeSpan.FromHours(4));
    }

    [Theory]
    [InlineData("signal:AAPL:20241227", true)]
    [InlineData("universe:today", true)]
    [InlineData("stop:MSFT", true)]
    [InlineData("vix:synthetic", true)]
    [InlineData("regime:today", true)]
    [InlineData("breadth:analysis:current", true)]
    [InlineData("execution:log:20241227", true)]
    [InlineData("vwap:TSLA", true)]
    [InlineData("volatility:metrics:NVDA", true)]
    [InlineData("premarket:analysis:GOOGL", true)]
    [InlineData("block:AMZN:20241227", true)]
    [InlineData("health_check_abc123", true)]
    [InlineData("breakout:status:META", true)]
    [InlineData("microstructure:analysis:NFLX", true)]
    [InlineData("microstructure:patterns:ORCL", true)]
    [InlineData("unknown:key:pattern", false)]
    [InlineData("random_key", false)]
    public void GetTTLForKey_ShouldReturnCorrectTTL(string key, bool shouldHaveTTL)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        if (shouldHaveTTL)
        {
            ttl.Should().NotBeNull();
            ttl.Should().BeGreaterThan(TimeSpan.Zero);
        }
        else
        {
            ttl.Should().BeNull();
        }
    }

    [Theory]
    [InlineData("signal:AAPL:20241227")]
    [InlineData("signal:SPY:20241225")]
    [InlineData("signal:QQQ:20250101")]
    public void GetTTLForKey_SignalKeys_ShouldReturnSignalTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Signal);
    }

    [Theory]
    [InlineData("universe:today")]
    [InlineData("universe:20241227")]
    [InlineData("polygon:symbols:full")]
    [InlineData("polygon:symbols:20241227")]
    public void GetTTLForKey_UniverseKeys_ShouldReturnUniverseTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Universe);
    }

    [Theory]
    [InlineData("stop:AAPL")]
    [InlineData("position:MSFT")]
    public void GetTTLForKey_StopKeys_ShouldReturnStopTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Stop);
    }

    [Theory]
    [InlineData("vix:synthetic")]
    [InlineData("vix:weights")]
    public void GetTTLForKey_SyntheticVixKeys_ShouldReturnSyntheticVixTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.SyntheticVix);
    }

    [Theory]
    [InlineData("vix:current")]
    [InlineData("vix:source:polygon")]
    [InlineData("vix:source:yahoo")]
    public void GetTTLForKey_VixDataKeys_ShouldReturnVixDataTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.VixData);
    }

    [Theory]
    [InlineData("regime:today")]
    [InlineData("index:regime:spx")]
    public void GetTTLForKey_RegimeKeys_ShouldReturnRegimeTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Regime);
    }

    [Theory]
    [InlineData("breadth:analysis:current")]
    [InlineData("breadth:stats:today")]
    public void GetTTLForKey_BreadthKeys_ShouldReturnBreadthTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Breadth);
    }

    [Theory]
    [InlineData("execution:log:20241227")]
    [InlineData("execution:metrics:today")]
    public void GetTTLForKey_ExecutionKeys_ShouldReturnExecutionTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Execution);
    }

    [Theory]
    [InlineData("health_check_abc123")]
    [InlineData("health_check_xyz789")]
    public void GetTTLForKey_HealthCheckKeys_ShouldReturnHealthCheckTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.HealthCheck);
    }

    [Theory]
    [InlineData("microstructure:patterns:AAPL")]
    [InlineData("microstructure:patterns:SPY")]
    public void GetTTLForKey_PatternKeys_ShouldReturnPatternsTTL(string key)
    {
        // Act
        var ttl = RedisKeyConstants.GetTTLForKey(key);

        // Assert
        ttl.Should().Be(RedisKeyConstants.RedisKeyTTL.Patterns);
    }

    [Fact]
    public void PatternToTTL_ShouldContainAllKeyPatterns()
    {
        // Arrange
        var expectedPatterns = new[]
        {
            RedisKeyConstants.KeyPatterns.Signal,
            RedisKeyConstants.KeyPatterns.Universe,
            RedisKeyConstants.KeyPatterns.PolygonSymbols,
            RedisKeyConstants.KeyPatterns.Stop,
            RedisKeyConstants.KeyPatterns.Position,
            RedisKeyConstants.KeyPatterns.VixSynthetic,
            RedisKeyConstants.KeyPatterns.VixWeights,
            RedisKeyConstants.KeyPatterns.VixCurrent,
            RedisKeyConstants.KeyPatterns.VixSource,
            RedisKeyConstants.KeyPatterns.Regime,
            RedisKeyConstants.KeyPatterns.IndexRegime,
            RedisKeyConstants.KeyPatterns.Breadth,
            RedisKeyConstants.KeyPatterns.Execution,
            RedisKeyConstants.KeyPatterns.Vwap,
            RedisKeyConstants.KeyPatterns.Volatility,
            RedisKeyConstants.KeyPatterns.PreMarket,
            RedisKeyConstants.KeyPatterns.Block,
            RedisKeyConstants.KeyPatterns.Throttle,
            RedisKeyConstants.KeyPatterns.HealthCheck,
            RedisKeyConstants.KeyPatterns.Breakout,
            RedisKeyConstants.KeyPatterns.Microstructure,
            RedisKeyConstants.KeyPatterns.MicrostructurePatterns
        };

        // Assert
        foreach (var pattern in expectedPatterns)
        {
            RedisKeyConstants.PatternToTTL.Should().ContainKey(pattern);
            RedisKeyConstants.PatternToTTL[pattern].Should().BeGreaterThan(TimeSpan.Zero);
        }

        RedisKeyConstants.PatternToTTL.Should().HaveCount(expectedPatterns.Length);
    }

    [Fact]
    public void KeyPatterns_ShouldHaveValidPatterns()
    {
        // Assert - Verify all patterns are properly formatted
        RedisKeyConstants.KeyPatterns.Signal.Should().Be("signal:*");
        RedisKeyConstants.KeyPatterns.Universe.Should().Be("universe:*");
        RedisKeyConstants.KeyPatterns.PolygonSymbols.Should().Be("polygon:symbols:*");
        RedisKeyConstants.KeyPatterns.Stop.Should().Be("stop:*");
        RedisKeyConstants.KeyPatterns.Position.Should().Be("position:*");
        RedisKeyConstants.KeyPatterns.VixSynthetic.Should().Be("vix:synthetic*");
        RedisKeyConstants.KeyPatterns.VixWeights.Should().Be("vix:weights*");
        RedisKeyConstants.KeyPatterns.VixCurrent.Should().Be("vix:current*");
        RedisKeyConstants.KeyPatterns.VixSource.Should().Be("vix:source:*");
        RedisKeyConstants.KeyPatterns.Regime.Should().Be("regime:*");
        RedisKeyConstants.KeyPatterns.IndexRegime.Should().Be("index:regime:*");
        RedisKeyConstants.KeyPatterns.Breadth.Should().Be("breadth:*");
        RedisKeyConstants.KeyPatterns.Execution.Should().Be("execution:*");
        RedisKeyConstants.KeyPatterns.Vwap.Should().Be("vwap:*");
        RedisKeyConstants.KeyPatterns.Volatility.Should().Be("volatility:*");
        RedisKeyConstants.KeyPatterns.PreMarket.Should().Be("premarket:*");
        RedisKeyConstants.KeyPatterns.Block.Should().Be("block:*");
        RedisKeyConstants.KeyPatterns.Throttle.Should().Be("throttle:*");
        RedisKeyConstants.KeyPatterns.HealthCheck.Should().Be("health_check_*");
        RedisKeyConstants.KeyPatterns.Breakout.Should().Be("breakout:*");
        RedisKeyConstants.KeyPatterns.Microstructure.Should().Be("microstructure:*");
        RedisKeyConstants.KeyPatterns.MicrostructurePatterns.Should().Be("microstructure:patterns:*");
    }
}
