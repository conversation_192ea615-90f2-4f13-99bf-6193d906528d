using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for QuoteVolatilityGuard with real Redis
/// Tests the complete flow from quote processing to halt key management
/// </summary>
[Collection("Redis")]
public class QuoteVolatilityGuardIntegrationTests : IAsyncDisposable
{
    private readonly IServiceProvider _serviceProvider;
    private readonly QuoteVolatilityGuard _guard;
    private readonly IDatabase _database;
    private readonly ILogger<QuoteVolatilityGuardIntegrationTests> _logger;

    public QuoteVolatilityGuardIntegrationTests()
    {
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["ConnectionStrings:Redis"] = "*************:6379"
            })
            .Build();

        // Setup DI container
        var services = new ServiceCollection();
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Register Redis connection
        services.AddSingleton<IConnectionMultiplexer>(provider =>
        {
            var connectionString = configuration.GetConnectionString("Redis") ?? "*************:6379";
            return ConnectionMultiplexer.Connect(connectionString);
        });

        // Register QuoteVolatilityGuard
        services.AddSingleton<QuoteVolatilityGuard>();

        _serviceProvider = services.BuildServiceProvider();
        _guard = _serviceProvider.GetRequiredService<QuoteVolatilityGuard>();
        _logger = _serviceProvider.GetRequiredService<ILogger<QuoteVolatilityGuardIntegrationTests>>();
        
        // Get Redis database for direct testing
        var connectionMultiplexer = _serviceProvider.GetRequiredService<IConnectionMultiplexer>();
        _database = connectionMultiplexer.GetDatabase();
    }

    [Fact]
    public async Task QuoteVolatilityGuard_EndToEndFlow_ShouldWorkCorrectly()
    {
        // Arrange
        const string testSymbol = "QVGTEST";
        const decimal basePrice = 100.00m;
        
        // Clean up any existing halt keys
        await _database.KeyDeleteAsync($"halt:{testSymbol}");

        try
        {
            _logger.LogInformation("Starting end-to-end QuoteVolatilityGuard test for {Symbol}", testSymbol);

            // Step 1: Feed normal quotes to establish baseline
            _logger.LogInformation("Step 1: Feeding normal quotes to establish baseline");
            for (int i = 0; i < 60; i++) // More than minimum window for reliable stats
            {
                var normalBid = basePrice - 0.05m;
                var normalAsk = basePrice + 0.05m;
                _guard.OnQuote(testSymbol, normalBid, normalAsk);
            }

            // Verify no halt initially
            var isHaltedInitially = await _guard.IsTradingHaltedAsync(testSymbol);
            isHaltedInitially.Should().BeFalse("No halt should be active initially");

            // Step 2: Feed extremely volatile spread
            _logger.LogInformation("Step 2: Feeding volatile spread to trigger halt");
            var volatileBid = basePrice - 5.00m; // Extremely wide spread
            var volatileAsk = basePrice + 5.00m;
            _guard.OnQuote(testSymbol, volatileBid, volatileAsk);

            // Wait for async halt operation to complete
            await Task.Delay(500);

            // Step 3: Verify halt was triggered
            _logger.LogInformation("Step 3: Verifying halt was triggered");
            var isHaltedAfterVolatility = await _guard.IsTradingHaltedAsync(testSymbol);
            isHaltedAfterVolatility.Should().BeTrue("Halt should be active after volatile spread");

            // Verify Redis key exists with correct value
            var haltValue = await _database.StringGetAsync($"halt:{testSymbol}");
            haltValue.Should().Be("volatility", "Halt key should have 'volatility' value");

            // Verify TTL is set correctly (should be around 2 minutes)
            var ttl = await _database.KeyTimeToLiveAsync($"halt:{testSymbol}");
            ttl.Should().NotBeNull("TTL should be set");
            ttl.Value.TotalMinutes.Should().BeInRange(1.5, 2.5, "TTL should be around 2 minutes");

            // Step 4: Test spread statistics
            _logger.LogInformation("Step 4: Testing spread statistics");
            var stats = _guard.GetSpreadStats(testSymbol);
            stats.Should().NotBeNull("Spread statistics should be available");
            stats.Value.Count.Should().BeGreaterThan(60, "Should have processed multiple quotes");
            stats.Value.WindowSize.Should().Be(120, "Window size should be 120");
            stats.Value.HasSufficientData.Should().BeTrue("Should have sufficient data for analysis");

            _logger.LogInformation("End-to-end test completed successfully");
        }
        finally
        {
            // Cleanup
            await _database.KeyDeleteAsync($"halt:{testSymbol}");
            _guard.ClearSpreadStats(testSymbol);
        }
    }

    [Fact]
    public async Task QuoteVolatilityGuard_WithMultipleSymbols_ShouldHandleIndependently()
    {
        // Arrange
        const string symbol1 = "QVGTEST1";
        const string symbol2 = "QVGTEST2";
        const decimal basePrice = 100.00m;

        // Clean up any existing halt keys
        await _database.KeyDeleteAsync($"halt:{symbol1}");
        await _database.KeyDeleteAsync($"halt:{symbol2}");

        try
        {
            _logger.LogInformation("Testing multiple symbols independence");

            // Feed normal quotes for both symbols
            for (int i = 0; i < 60; i++)
            {
                _guard.OnQuote(symbol1, basePrice - 0.05m, basePrice + 0.05m);
                _guard.OnQuote(symbol2, basePrice - 0.05m, basePrice + 0.05m);
            }

            // Make only symbol1 volatile
            _guard.OnQuote(symbol1, basePrice - 5.00m, basePrice + 5.00m);

            // Wait for async operations
            await Task.Delay(500);

            // Verify only symbol1 is halted
            var symbol1Halted = await _guard.IsTradingHaltedAsync(symbol1);
            var symbol2Halted = await _guard.IsTradingHaltedAsync(symbol2);

            symbol1Halted.Should().BeTrue("Symbol1 should be halted");
            symbol2Halted.Should().BeFalse("Symbol2 should not be halted");

            // Verify independent statistics
            var stats1 = _guard.GetSpreadStats(symbol1);
            var stats2 = _guard.GetSpreadStats(symbol2);

            stats1.Should().NotBeNull();
            stats2.Should().NotBeNull();
            stats1.Value.Count.Should().Be(stats2.Value.Count, "Both should have same number of quotes");
        }
        finally
        {
            // Cleanup
            await _database.KeyDeleteAsync($"halt:{symbol1}");
            await _database.KeyDeleteAsync($"halt:{symbol2}");
            _guard.ClearSpreadStats(symbol1);
            _guard.ClearSpreadStats(symbol2);
        }
    }

    [Fact]
    public async Task QuoteVolatilityGuard_HaltExpiration_ShouldAllowTradingAfterTTL()
    {
        // Arrange
        const string testSymbol = "QVGTTL";
        
        // Clean up any existing halt keys
        await _database.KeyDeleteAsync($"halt:{testSymbol}");

        try
        {
            // Manually set a halt key with short TTL for testing
            await _database.StringSetAsync($"halt:{testSymbol}", "volatility", TimeSpan.FromSeconds(2));

            // Verify halt is active
            var isHaltedInitially = await _guard.IsTradingHaltedAsync(testSymbol);
            isHaltedInitially.Should().BeTrue("Halt should be active initially");

            // Wait for TTL to expire
            await Task.Delay(3000);

            // Verify halt has expired
            var isHaltedAfterExpiry = await _guard.IsTradingHaltedAsync(testSymbol);
            isHaltedAfterExpiry.Should().BeFalse("Halt should have expired");

            // Verify key no longer exists
            var keyExists = await _database.KeyExistsAsync($"halt:{testSymbol}");
            keyExists.Should().BeFalse("Halt key should have expired");
        }
        finally
        {
            // Cleanup
            await _database.KeyDeleteAsync($"halt:{testSymbol}");
        }
    }

    [Fact]
    public async Task QuoteVolatilityGuard_InteractionWithAnomalyDetector_ShouldCoexist()
    {
        // Arrange
        const string testSymbol = "QVGAD";
        
        // Clean up any existing halt keys
        await _database.KeyDeleteAsync($"halt:{testSymbol}");

        try
        {
            // Simulate AnomalyDetectorService setting a halt
            await _database.StringSetAsync($"halt:{testSymbol}", "1", TimeSpan.FromMinutes(5));

            // QuoteVolatilityGuard should recognize the key exists but not claim it as its own
            var isHaltedByQuoteGuard = await _guard.IsTradingHaltedAsync(testSymbol);
            isHaltedByQuoteGuard.Should().BeFalse("QuoteVolatilityGuard should not recognize AnomalyDetector halt");

            // But the key should still exist
            var keyExists = await _database.KeyExistsAsync($"halt:{testSymbol}");
            keyExists.Should().BeTrue("Halt key should still exist");

            // Now let QuoteVolatilityGuard set its own halt (should overwrite)
            await _database.StringSetAsync($"halt:{testSymbol}", "volatility", TimeSpan.FromMinutes(2));

            var isHaltedByQuoteGuardAfter = await _guard.IsTradingHaltedAsync(testSymbol);
            isHaltedByQuoteGuardAfter.Should().BeTrue("QuoteVolatilityGuard should now recognize its own halt");
        }
        finally
        {
            // Cleanup
            await _database.KeyDeleteAsync($"halt:{testSymbol}");
        }
    }

    public async ValueTask DisposeAsync()
    {
        // Clean up any test keys that might remain
        var server = _database.Multiplexer.GetServer(_database.Multiplexer.GetEndPoints().First());
        var keys = server.Keys(pattern: "halt:QVG*").ToArray();
        
        if (keys.Length > 0)
        {
            await _database.KeyDeleteAsync(keys);
            _logger.LogInformation("Cleaned up {Count} test halt keys", keys.Length);
        }

        _guard?.Dispose();
        
        if (_serviceProvider is IDisposable disposable)
        {
            disposable.Dispose();
        }
    }
}
