using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for RealTimeMarketMonitor
/// Tests market monitoring, alert generation, background service functionality, and event handling
/// </summary>
[Trait("Category","Legacy")]
public class RealTimeMarketMonitorTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<ILogger<RealTimeMarketMonitor>> _mockLogger;
    private readonly RealTimeMarketMonitor _service;
    private readonly MarketMonitorConfig _config;

    public RealTimeMarketMonitorTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockLogger = new Mock<ILogger<RealTimeMarketMonitor>>();
        
        _config = new MarketMonitorConfig(
            UpdateInterval: TimeSpan.FromSeconds(1),
            MaxPriceHistoryPoints: 50,
            MaxAlertsPerCycle: 5,
            MaxAlertQueueSize: 100,
            HighVolatilityThreshold: 0.03m,
            TrendChangeThreshold: 0.02m
        );

        _service = new RealTimeMarketMonitor(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            _config
        );
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task AddSymbolToMonitorAsync_ShouldAddSymbolSuccessfully()
    {
        // Arrange
        var symbol = "AAPL";
        var criteria = new MonitoringCriteria(
            PriceMovementThreshold: 0.02m,
            MonitorPriceMovements: true,
            MonitorVolatility: true,
            MonitorTrendChanges: true
        );

        // Act
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Assert
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().NotBeNull();
        snapshot!.Symbol.Should().Be(symbol);
        snapshot.MonitoringCriteria.Should().BeEquivalentTo(criteria);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task AddSymbolToMonitorAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var symbol = "ERRORTEST";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);

        // Act - Should not throw
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Assert - Should complete without throwing
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RemoveSymbolFromMonitorAsync_ShouldRemoveSymbol()
    {
        // Arrange
        var symbol = "AAPL";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Act
        await _service.RemoveSymbolFromMonitorAsync(symbol);

        // Assert
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().BeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RemoveSymbolFromMonitorAsync_WithNonExistentSymbol_ShouldHandleGracefully()
    {
        // Act - Should not throw
        await _service.RemoveSymbolFromMonitorAsync("NONEXISTENT");

        // Assert - Should complete without throwing
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void GetMarketSnapshot_WithExistingSymbol_ShouldReturnSnapshot()
    {
        // Arrange
        var symbol = "AAPL";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);
        _service.AddSymbolToMonitorAsync(symbol, criteria).Wait();

        // Act
        var snapshot = _service.GetMarketSnapshot(symbol);

        // Assert
        snapshot.Should().NotBeNull();
        snapshot!.Symbol.Should().Be(symbol);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void GetMarketSnapshot_WithNonExistentSymbol_ShouldReturnNull()
    {
        // Act
        var snapshot = _service.GetMarketSnapshot("NONEXISTENT");

        // Assert
        snapshot.Should().BeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void GetAllMarketSnapshots_InitiallyEmpty_ShouldReturnEmptyDictionary()
    {
        // Act
        var snapshots = _service.GetAllMarketSnapshots();

        // Assert
        snapshots.Should().NotBeNull();
        snapshots.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task GetAllMarketSnapshots_WithMultipleSymbols_ShouldReturnAllSnapshots()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "TSLA" };
        var criteria = new MonitoringCriteria(0.02m, true, true, true);

        foreach (var symbol in symbols)
        {
            await _service.AddSymbolToMonitorAsync(symbol, criteria);
        }

        // Act
        var snapshots = _service.GetAllMarketSnapshots();

        // Assert
        snapshots.Should().HaveCount(3);
        snapshots.Should().ContainKeys(symbols);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void GetRecentAlerts_InitiallyEmpty_ShouldReturnEmptyList()
    {
        // Act
        var alerts = _service.GetRecentAlerts();

        // Assert
        alerts.Should().NotBeNull();
        alerts.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void GetRecentAlerts_WithCountLimit_ShouldRespectLimit()
    {
        // Act
        var alerts = _service.GetRecentAlerts(10);

        // Assert
        alerts.Should().NotBeNull();
        alerts.Should().HaveCountLessOrEqualTo(10);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void AlertGenerated_Event_ShouldBeRaisable()
    {
        // Arrange
        MarketAlertEventArgs? capturedArgs = null;
        _service.AlertGenerated += (sender, args) => capturedArgs = args;

        // Act - Events would be triggered by actual alert processing
        // For testing, we verify they can be subscribed to

        // Assert
        capturedArgs.Should().BeNull(); // No events fired yet
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void MarketConditionChanged_Event_ShouldBeRaisable()
    {
        // Arrange
        MarketConditionEventArgs? capturedArgs = null;
        _service.MarketConditionChanged += (sender, args) => capturedArgs = args;

        // Act - Events would be triggered by actual condition changes
        // For testing, we verify they can be subscribed to

        // Assert
        capturedArgs.Should().BeNull(); // No events fired yet
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void SignificantPriceMovement_Event_ShouldBeRaisable()
    {
        // Arrange
        PriceMovementEventArgs? capturedArgs = null;
        _service.SignificantPriceMovement += (sender, args) => capturedArgs = args;

        // Act - Events would be triggered by actual price movements
        // For testing, we verify they can be subscribed to

        // Assert
        capturedArgs.Should().BeNull(); // No events fired yet
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Theory]
    [InlineData(MarketTrend.Bullish)]
    [InlineData(MarketTrend.Bearish)]
    [InlineData(MarketTrend.Sideways)]
    [InlineData(MarketTrend.Unknown)]
    public async Task AddSymbolToMonitorAsync_ShouldHandleAllTrendTypes(MarketTrend expectedTrend)
    {
        // Arrange
        var symbol = $"TEST_{expectedTrend}";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);

        // Act
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Assert
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().NotBeNull();
        snapshot!.Trend.Should().Be(MarketTrend.Unknown); // Initially unknown
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Theory]
    [InlineData(0.01, true, true, true)]
    [InlineData(0.05, false, true, false)]
    [InlineData(0.10, true, false, true)]
    [InlineData(0.02, false, false, false)]
    public async Task AddSymbolToMonitorAsync_WithVariousCriteria_ShouldStoreCorrectly(
        decimal threshold, bool monitorPrice, bool monitorVolatility, bool monitorTrend)
    {
        // Arrange
        var symbol = "CRITERIA_TEST";
        var criteria = new MonitoringCriteria(threshold, monitorPrice, monitorVolatility, monitorTrend);

        // Act
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Assert
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().NotBeNull();
        snapshot!.MonitoringCriteria.PriceMovementThreshold.Should().Be(threshold);
        snapshot.MonitoringCriteria.MonitorPriceMovements.Should().Be(monitorPrice);
        snapshot.MonitoringCriteria.MonitorVolatility.Should().Be(monitorVolatility);
        snapshot.MonitoringCriteria.MonitorTrendChanges.Should().Be(monitorTrend);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task AddSymbolToMonitorAsync_WithDuplicateSymbol_ShouldUpdateExisting()
    {
        // Arrange
        var symbol = "DUPLICATE";
        var criteria1 = new MonitoringCriteria(0.01m, true, false, false);
        var criteria2 = new MonitoringCriteria(0.05m, false, true, true);

        // Act
        await _service.AddSymbolToMonitorAsync(symbol, criteria1);
        await _service.AddSymbolToMonitorAsync(symbol, criteria2);

        // Assert
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().NotBeNull();
        snapshot!.MonitoringCriteria.Should().BeEquivalentTo(criteria2); // Should use latest criteria
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RemoveSymbolFromMonitorAsync_WithException_ShouldHandleGracefully()
    {
        // Arrange
        var symbol = "REMOVE_ERROR";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Act - Should not throw even if there are internal errors
        await _service.RemoveSymbolFromMonitorAsync(symbol);

        // Assert - Should complete without throwing
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Configuration_ShouldUseProvidedValues()
    {
        // Arrange
        var customConfig = new MarketMonitorConfig(
            UpdateInterval: TimeSpan.FromMinutes(2),
            MaxPriceHistoryPoints: 200,
            MaxAlertsPerCycle: 20,
            MaxAlertQueueSize: 2000,
            HighVolatilityThreshold: 0.10m,
            TrendChangeThreshold: 0.05m
        );

        // Act
        var serviceWithCustomConfig = new RealTimeMarketMonitor(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            customConfig
        );

        // Assert - Service should be created with custom configuration
        serviceWithCustomConfig.Should().NotBeNull();
        serviceWithCustomConfig.Dispose();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void Configuration_WithNullConfig_ShouldUseDefaults()
    {
        // Act
        var serviceWithDefaultConfig = new RealTimeMarketMonitor(
            _mockMarketDataService.Object,
            _mockLiveStateStore.Object,
            _mockLogger.Object,
            null // Null config should use defaults
        );

        // Assert - Service should be created with default configuration
        serviceWithDefaultConfig.Should().NotBeNull();
        serviceWithDefaultConfig.Dispose();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task AddSymbolToMonitorAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "CONCURRENT";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);

        // Act - Simulate concurrent additions
        var tasks = new List<Task>();
        for (int i = 0; i < 10; i++)
        {
            tasks.Add(_service.AddSymbolToMonitorAsync(symbol, criteria));
        }

        await Task.WhenAll(tasks);

        // Assert
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().NotBeNull();
        snapshot!.Symbol.Should().Be(symbol);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RemoveSymbolFromMonitorAsync_WithConcurrentCalls_ShouldHandleSafely()
    {
        // Arrange
        var symbol = "CONCURRENT_REMOVE";
        var criteria = new MonitoringCriteria(0.02m, true, true, true);
        await _service.AddSymbolToMonitorAsync(symbol, criteria);

        // Act - Simulate concurrent removals
        var tasks = new List<Task>();
        for (int i = 0; i < 5; i++)
        {
            tasks.Add(_service.RemoveSymbolFromMonitorAsync(symbol));
        }

        await Task.WhenAll(tasks);

        // Assert - Should complete without throwing
        var snapshot = _service.GetMarketSnapshot(symbol);
        snapshot.Should().BeNull();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task GetAllMarketSnapshots_WithLargeNumberOfSymbols_ShouldHandleEfficiently()
    {
        // Arrange
        var symbols = Enumerable.Range(1, 100).Select(i => $"SYM{i:D3}").ToArray();
        var criteria = new MonitoringCriteria(0.02m, true, true, true);

        foreach (var symbol in symbols)
        {
            await _service.AddSymbolToMonitorAsync(symbol, criteria);
        }

        // Act
        var snapshots = _service.GetAllMarketSnapshots();

        // Assert
        snapshots.Should().HaveCount(100);
        snapshots.Keys.Should().Contain(symbols);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Theory]
    [InlineData(MarketAlertType.PriceMovement)]
    [InlineData(MarketAlertType.HighVolatility)]
    [InlineData(MarketAlertType.TrendChange)]
    [InlineData(MarketAlertType.VolumeSpike)]
    [InlineData(MarketAlertType.TechnicalBreakout)]
    public void MarketAlertType_ShouldSupportAllTypes(MarketAlertType alertType)
    {
        // This test verifies all alert types are supported
        // In a real scenario, these would be generated by the monitoring logic

        // Act & Assert - Should be able to create alerts of all types
        var alert = new MarketAlert(
            "TEST", alertType, "Test alert", 100.00m, DateTime.UtcNow, AlertSeverity.Medium);

        alert.AlertType.Should().Be(alertType);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Theory]
    [InlineData(AlertSeverity.Low)]
    [InlineData(AlertSeverity.Medium)]
    [InlineData(AlertSeverity.High)]
    [InlineData(AlertSeverity.Critical)]
    public void AlertSeverity_ShouldSupportAllLevels(AlertSeverity severity)
    {
        // This test verifies all severity levels are supported

        // Act & Assert - Should be able to create alerts with all severity levels
        var alert = new MarketAlert(
            "TEST", MarketAlertType.PriceMovement, "Test alert", 100.00m, DateTime.UtcNow, severity);

        alert.Severity.Should().Be(severity);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Theory]
    [InlineData(MarketCondition.Calm)]
    [InlineData(MarketCondition.Normal)]
    [InlineData(MarketCondition.Elevated)]
    [InlineData(MarketCondition.Stressed)]
    [InlineData(MarketCondition.Crisis)]
    public void MarketCondition_ShouldSupportAllConditions(MarketCondition condition)
    {
        // This test verifies all market conditions are supported

        // Act & Assert - Should be able to work with all market conditions
        var eventArgs = new MarketConditionEventArgs(condition, 20.0m, DateTime.UtcNow);
        eventArgs.Condition.Should().Be(condition);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void MarketSnapshot_ShouldInitializeCorrectly()
    {
        // Arrange
        var symbol = "TEST";
        var currentPrice = 150.00m;
        var priceChange = 0.02m;
        var lastUpdated = DateTime.UtcNow;
        var criteria = new MonitoringCriteria(0.02m, true, true, true);
        var priceHistory = new List<PricePoint>();
        var trend = MarketTrend.Bullish;
        var volatility = 0.025m;

        // Act
        var snapshot = new MarketSnapshot(
            symbol, currentPrice, priceChange, lastUpdated, criteria, priceHistory, trend, volatility);

        // Assert
        snapshot.Symbol.Should().Be(symbol);
        snapshot.CurrentPrice.Should().Be(currentPrice);
        snapshot.PriceChange.Should().Be(priceChange);
        snapshot.LastUpdated.Should().Be(lastUpdated);
        snapshot.MonitoringCriteria.Should().BeEquivalentTo(criteria);
        snapshot.PriceHistory.Should().BeEquivalentTo(priceHistory);
        snapshot.Trend.Should().Be(trend);
        snapshot.Volatility.Should().Be(volatility);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void PricePoint_ShouldInitializeCorrectly()
    {
        // Arrange
        var price = 150.00m;
        var timestamp = DateTime.UtcNow;

        // Act
        var pricePoint = new PricePoint(price, timestamp);

        // Assert
        pricePoint.Price.Should().Be(price);
        pricePoint.Timestamp.Should().Be(timestamp);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void MonitoringCriteria_ShouldInitializeCorrectly()
    {
        // Arrange
        var threshold = 0.05m;
        var monitorPrice = true;
        var monitorVolatility = false;
        var monitorTrend = true;

        // Act
        var criteria = new MonitoringCriteria(threshold, monitorPrice, monitorVolatility, monitorTrend);

        // Assert
        criteria.PriceMovementThreshold.Should().Be(threshold);
        criteria.MonitorPriceMovements.Should().Be(monitorPrice);
        criteria.MonitorVolatility.Should().Be(monitorVolatility);
        criteria.MonitorTrendChanges.Should().Be(monitorTrend);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void MarketAlert_ShouldInitializeCorrectly()
    {
        // Arrange
        var symbol = "AAPL";
        var alertType = MarketAlertType.PriceMovement;
        var description = "Price moved 5%";
        var price = 150.00m;
        var timestamp = DateTime.UtcNow;
        var severity = AlertSeverity.High;

        // Act
        var alert = new MarketAlert(symbol, alertType, description, price, timestamp, severity);

        // Assert
        alert.Symbol.Should().Be(symbol);
        alert.AlertType.Should().Be(alertType);
        alert.Description.Should().Be(description);
        alert.Price.Should().Be(price);
        alert.Timestamp.Should().Be(timestamp);
        alert.Severity.Should().Be(severity);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public void EventArgs_ShouldInitializeCorrectly()
    {
        // Arrange
        var alert = new MarketAlert("AAPL", MarketAlertType.PriceMovement, "Test", 150.00m, DateTime.UtcNow, AlertSeverity.Medium);
        var condition = MarketCondition.Normal;
        var vixLevel = 18.5m;
        var timestamp = DateTime.UtcNow;

        // Act
        var alertEventArgs = new MarketAlertEventArgs(alert);
        var conditionEventArgs = new MarketConditionEventArgs(condition, vixLevel, timestamp);
        var priceMovementEventArgs = new PriceMovementEventArgs("AAPL", 148.00m, 150.00m, 0.0135m);

        // Assert
        alertEventArgs.Alert.Should().Be(alert);
        conditionEventArgs.Condition.Should().Be(condition);
        conditionEventArgs.VixLevel.Should().Be(vixLevel);
        conditionEventArgs.Timestamp.Should().Be(timestamp);
        priceMovementEventArgs.Symbol.Should().Be("AAPL");
        priceMovementEventArgs.PreviousPrice.Should().Be(148.00m);
        priceMovementEventArgs.CurrentPrice.Should().Be(150.00m);
        priceMovementEventArgs.PercentChange.Should().Be(0.0135m);
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
