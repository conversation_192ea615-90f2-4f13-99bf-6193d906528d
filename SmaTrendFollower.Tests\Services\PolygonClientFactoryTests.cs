using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

[Trait("Category","Legacy")]
public class PolygonClientFactoryTests
{
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ILogger<PolygonRateLimitHelper>> _mockLogger;
    private readonly Mock<ILogger<PolygonWebSocketClient>> _mockWebSocketLogger;
    private readonly Mock<HttpClient> _mockHttpClient;

    public PolygonClientFactoryTests()
    {
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockLogger = new Mock<ILogger<PolygonRateLimitHelper>>();
        _mockWebSocketLogger = new Mock<ILogger<PolygonWebSocketClient>>();
        _mockHttpClient = new Mock<HttpClient>();

        _mockHttpClientFactory.Setup(x => x.CreateClient("polygon"))
            .Returns(_mockHttpClient.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void AddApiKeyToUrl_WithApiKey_ShouldAppendQueryParameter()
    {
        // Arrange
        var apiKey = "test-api-key";
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns(apiKey);
        
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);
        var baseUrl = "v2/aggs/ticker/AAPL/range/1/day/2023-01-01/2023-01-31";

        // Act
        var result = factory.AddApiKeyToUrl(baseUrl);

        // Assert
        result.Should().Be($"{baseUrl}?apiKey={apiKey}");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void AddApiKeyToUrl_WithExistingQueryParams_ShouldAppendWithAmpersand()
    {
        // Arrange
        var apiKey = "test-api-key";
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns(apiKey);
        
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);
        var baseUrl = "v2/aggs/ticker/AAPL/range/1/day/2023-01-01/2023-01-31?adjusted=true&sort=asc";

        // Act
        var result = factory.AddApiKeyToUrl(baseUrl);

        // Assert
        result.Should().Be($"{baseUrl}&apiKey={apiKey}");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void AddApiKeyToUrl_WithoutApiKey_ShouldReturnOriginalUrl()
    {
        // Arrange
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns((string?)null);
        
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);
        var baseUrl = "v2/aggs/ticker/AAPL/range/1/day/2023-01-01/2023-01-31";

        // Act
        var result = factory.AddApiKeyToUrl(baseUrl);

        // Assert
        result.Should().Be(baseUrl);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void AddApiKeyToUrl_WithEmptyApiKey_ShouldReturnOriginalUrl()
    {
        // Arrange
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns("");
        
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);
        var baseUrl = "v2/aggs/ticker/AAPL/range/1/day/2023-01-01/2023-01-31";

        // Act
        var result = factory.AddApiKeyToUrl(baseUrl);

        // Assert
        result.Should().Be(baseUrl);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void CreateClient_ShouldReturnHttpClient()
    {
        // Arrange
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns("test-key");
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);

        // Act
        var result = factory.CreateClient();

        // Assert
        result.Should().NotBeNull();
        _mockHttpClientFactory.Verify(x => x.CreateClient("polygon"), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void GetRateLimitHelper_ShouldReturnHelper()
    {
        // Arrange
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns("test-key");
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);

        // Act
        var result = factory.GetRateLimitHelper();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeAssignableTo<IPolygonRateLimitHelper>();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Arrange
        _mockConfiguration.Setup(x => x["POLY_API_KEY"]).Returns("test-key");
        var factory = new PolygonClientFactory(_mockHttpClientFactory.Object, _mockConfiguration.Object, _mockLogger.Object, _mockWebSocketLogger.Object);

        // Act & Assert
        var action = () => factory.Dispose();
        action.Should().NotThrow();
    }
}
