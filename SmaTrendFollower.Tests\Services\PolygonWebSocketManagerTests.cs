using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using System.Net.WebSockets;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive unit tests for PolygonWebSocketManager
/// Tests batch subscription, reconnection, Redis persistence, rate limiting, and error scenarios
/// </summary>
[Trait("Category","Legacy")]
public class PolygonWebSocketManagerTests : IDisposable
{
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly Mock<ILogger<PolygonWebSocketManager>> _mockLogger;
    private readonly Mock<ITradingMetricsService> _mockMetricsService;
    private readonly PolygonWebSocketManager _manager;
    private readonly string _testApiKey = "test-api-key";

    public PolygonWebSocketManagerTests()
    {
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockDatabase = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<PolygonWebSocketManager>>();
        _mockMetricsService = new Mock<ITradingMetricsService>();

        _mockRedisService.Setup(x => x.GetDatabaseAsync(0))
            .ReturnsAsync(_mockDatabase.Object);

        _manager = new PolygonWebSocketManager(
            _mockRedisService.Object,
            _mockLogger.Object,
            _testApiKey,
            _mockMetricsService.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitialize()
    {
        // Act & Assert
        _manager.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullRedisService_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PolygonWebSocketManager(
            null!,
            _mockLogger.Object,
            _testApiKey);

        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("redisService");
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PolygonWebSocketManager(
            _mockRedisService.Object,
            null!,
            _testApiKey);

        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("logger");
    }

    [Fact]
    public void Constructor_WithNullApiKey_ShouldThrowArgumentNullException()
    {
        // Act & Assert
        var act = () => new PolygonWebSocketManager(
            _mockRedisService.Object,
            _mockLogger.Object,
            null!);

        act.Should().Throw<ArgumentNullException>()
            .WithParameterName("apiKey");
    }

    [Fact]
    public void GetConnectionState_WithNoConnection_ShouldReturnNone()
    {
        // Act
        var state = _manager.GetConnectionState(PolygonWsChannel.EquityTrades);

        // Assert
        state.Should().Be(WebSocketState.None);
    }

    [Fact]
    public void GetSubscribedSymbolCount_WithNoConnection_ShouldReturnZero()
    {
        // Act
        var count = _manager.GetSubscribedSymbolCount(PolygonWsChannel.EquityTrades);

        // Assert
        count.Should().Be(0);
    }

    [Fact]
    public async Task BatchSubscribeAsync_WithEmptySymbols_ShouldLogWarningAndReturn()
    {
        // Arrange
        var emptySymbols = new List<string>();

        // Act
        await _manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, emptySymbols);

        // Assert
        VerifyLogContains("No symbols provided for batch subscription");
    }

    [Fact]
    public async Task BatchSubscribeAsync_WithDisposedManager_ShouldThrowObjectDisposedException()
    {
        // Arrange
        _manager.Dispose();
        var symbols = new[] { "AAPL", "MSFT" };

        // Act & Assert
        var act = async () => await _manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols);
        await act.Should().ThrowAsync<ObjectDisposedException>();
    }

    [Theory]
    [InlineData(PolygonWsChannel.EquityTrades)]
    [InlineData(PolygonWsChannel.EquityQuotes)]
    [InlineData(PolygonWsChannel.EquityMinute)]
    [InlineData(PolygonWsChannel.IndexMinute)]
    public async Task BatchSubscribeAsync_WithValidChannel_ShouldPersistToRedis(PolygonWsChannel channel)
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var expectedRedisKey = channel.GetRedisSubscriptionKey();
        var expectedValue = string.Join(",", symbols);

        _mockDatabase.Setup(x => x.StringSetAsync(
            expectedRedisKey,
            expectedValue,
            RedisKeyConstants.RedisKeyTTL.WebSocketSubscriptions,
            When.Always,
            CommandFlags.None))
            .ReturnsAsync(true);

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(channel, symbols);
        }
        catch (Exception)
        {
            // Expected to fail due to WebSocket connection, but Redis persistence should still be called
        }

        // Assert
        _mockDatabase.Verify(x => x.StringSetAsync(
            expectedRedisKey,
            expectedValue,
            RedisKeyConstants.RedisKeyTTL.WebSocketSubscriptions,
            When.Always,
            CommandFlags.None), Times.Once);
    }

    [Fact]
    public async Task BatchSubscribeAsync_WithLargeSymbolList_ShouldChunkInto500SymbolBatches()
    {
        // Arrange
        var symbols = Enumerable.Range(1, 1250)
            .Select(i => $"SYMBOL{i:D4}")
            .ToArray();

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols);
        }
        catch (Exception)
        {
            // Expected to fail due to WebSocket connection
        }

        // Assert
        VerifyLogContains("Batch subscribing 1250 symbols in 3 chunk(s)");
    }

    [Fact]
    public async Task BatchSubscribeAsync_ShouldRecordMetrics()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        var channel = PolygonWsChannel.EquityTrades;

        // Act
        try
        {
            await _manager.BatchSubscribeAsync(channel, symbols);
        }
        catch (Exception)
        {
            // Expected to fail due to WebSocket connection
        }

        // Assert
        _mockMetricsService.Verify(x => x.RecordSystemMetricAsync(
            $"websocket_subscribed_symbols_{channel}",
            symbols.Length,
            "count"), Times.Once);
    }

    [Fact]
    public async Task DisconnectAllAsync_ShouldLogDisconnectionMessage()
    {
        // Act
        await _manager.DisconnectAllAsync();

        // Assert
        VerifyLogContains("All WebSocket connections disconnected");
    }

    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var act = () => _manager.Dispose();
        act.Should().NotThrow();
    }

    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert
        var act = () =>
        {
            _manager.Dispose();
            _manager.Dispose();
            _manager.Dispose();
        };
        act.Should().NotThrow();
    }

    private void VerifyLogContains(string expectedMessage)
    {
        _mockLogger.Verify(
            x => x.Log(
                It.IsAny<LogLevel>(),
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(expectedMessage)),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.AtLeastOnce);
    }

    public void Dispose()
    {
        _manager?.Dispose();
    }
}

/// <summary>
/// Unit tests for PolygonWsChannel extension methods
/// </summary>
public class PolygonWsChannelExtensionsTests
{
    [Theory]
    [InlineData(PolygonWsChannel.EquityTrades, "wss://socket.polygon.io/stocks")]
    [InlineData(PolygonWsChannel.EquityQuotes, "wss://socket.polygon.io/stocks")]
    [InlineData(PolygonWsChannel.EquityMinute, "wss://socket.polygon.io/stocks")]
    [InlineData(PolygonWsChannel.IndexMinute, "wss://socket.polygon.io/indices")]
    public void GetWebSocketUrl_ShouldReturnCorrectUrl(PolygonWsChannel channel, string expectedUrl)
    {
        // Act
        var url = channel.GetWebSocketUrl();

        // Assert
        url.Should().Be(expectedUrl);
    }

    [Theory]
    [InlineData(PolygonWsChannel.EquityTrades, "T.")]
    [InlineData(PolygonWsChannel.EquityQuotes, "Q.")]
    [InlineData(PolygonWsChannel.EquityMinute, "AM.")]
    [InlineData(PolygonWsChannel.IndexMinute, "AM.I:")]
    public void GetSubscriptionPrefix_ShouldReturnCorrectPrefix(PolygonWsChannel channel, string expectedPrefix)
    {
        // Act
        var prefix = channel.GetSubscriptionPrefix();

        // Assert
        prefix.Should().Be(expectedPrefix);
    }

    [Theory]
    [InlineData(PolygonWsChannel.EquityTrades, "ws:subs:EquityTrades")]
    [InlineData(PolygonWsChannel.EquityQuotes, "ws:subs:EquityQuotes")]
    [InlineData(PolygonWsChannel.EquityMinute, "ws:subs:EquityMinute")]
    [InlineData(PolygonWsChannel.IndexMinute, "ws:subs:IndexMinute")]
    public void GetRedisSubscriptionKey_ShouldReturnCorrectKey(PolygonWsChannel channel, string expectedKey)
    {
        // Act
        var key = channel.GetRedisSubscriptionKey();

        // Assert
        key.Should().Be(expectedKey);
    }

    [Theory]
    [InlineData(PolygonWsChannel.EquityTrades, "Equity Trade Updates")]
    [InlineData(PolygonWsChannel.EquityQuotes, "Equity Quote Updates")]
    [InlineData(PolygonWsChannel.EquityMinute, "Equity Minute Aggregates")]
    [InlineData(PolygonWsChannel.IndexMinute, "Index Minute Aggregates")]
    public void GetDescription_ShouldReturnCorrectDescription(PolygonWsChannel channel, string expectedDescription)
    {
        // Act
        var description = channel.GetDescription();

        // Assert
        description.Should().Be(expectedDescription);
    }

    [Fact]
    public void FormatSubscriptionParams_WithEquityTrades_ShouldFormatCorrectly()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var channel = PolygonWsChannel.EquityTrades;

        // Act
        var result = channel.FormatSubscriptionParams(symbols);

        // Assert
        result.Should().Be("T.AAPL,T.MSFT,T.GOOGL");
    }

    [Fact]
    public void FormatSubscriptionParams_WithIndexMinute_ShouldFormatCorrectly()
    {
        // Arrange
        var symbols = new[] { "VIX", "SPX" };
        var channel = PolygonWsChannel.IndexMinute;

        // Act
        var result = channel.FormatSubscriptionParams(symbols);

        // Assert
        result.Should().Be("AM.I:VIX,AM.I:SPX");
    }

    [Fact]
    public void FormatSubscriptionParams_WithEmptySymbols_ShouldReturnEmptyString()
    {
        // Arrange
        var symbols = Array.Empty<string>();
        var channel = PolygonWsChannel.EquityTrades;

        // Act
        var result = channel.FormatSubscriptionParams(symbols);

        // Assert
        result.Should().BeEmpty();
    }
}
