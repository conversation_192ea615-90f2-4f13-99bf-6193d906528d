using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using FluentAssertions;
using StackExchange.Redis;
using System.Net;
using System.Text.Json;
using Xunit;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Tests.Services;

[Trait("Category","Legacy")]
public class SyntheticVixServiceTests : IDisposable
{
    private readonly Mock<IHttpClientFactory> _mockHttpClientFactory;
    private readonly Mock<HttpClient> _mockHttpClient;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<ConnectionMultiplexer> _mockConnectionMultiplexer;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly Mock<ILogger<SyntheticVixService>> _mockLogger;
    private readonly SyntheticVixService _service;

    public SyntheticVixServiceTests()
    {
        _mockHttpClientFactory = new Mock<IHttpClientFactory>();
        _mockHttpClient = new Mock<HttpClient>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockConnectionMultiplexer = new Mock<ConnectionMultiplexer>();
        _mockDatabase = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<SyntheticVixService>>();

        // Setup configuration
        _mockConfiguration.Setup(c => c["POLYGON_API_KEY"]).Returns("test-api-key");

        // Setup Redis
        _mockConnectionMultiplexer.Setup(c => c.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
            .Returns(_mockDatabase.Object);

        // Setup HTTP client factory
        _mockHttpClientFactory.Setup(f => f.CreateClient("Polygon"))
            .Returns(_mockHttpClient.Object);

        _service = new SyntheticVixService(
            _mockHttpClientFactory.Object,
            _mockConfiguration.Object,
            _mockConnectionMultiplexer.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task EstimateAsync_WithTrainedWeights_ShouldReturnAccurateEstimate()
    {
        // Arrange
        var trainedWeights = new SyntheticVixWeights
        {
            VxxCoefficient = 1.45m,
            UvxyCoefficient = 0.30m,
            SvxyCoefficient = -0.20m,
            SpyCoefficient = -0.05m,
            Intercept = 8.2m,
            TrainedAt = DateTime.UtcNow.AddDays(-1),
            RSquared = 0.85m,
            SampleSize = 252
        };

        var weightsJson = JsonSerializer.Serialize(trainedWeights);
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync(weightsJson);

        // Mock ETF price responses
        SetupEtfPriceResponse("VXX", 25.50m, DateTime.UtcNow.AddMinutes(-5));
        SetupEtfPriceResponse("UVXY", 12.75m, DateTime.UtcNow.AddMinutes(-3));
        SetupEtfPriceResponse("SVXY", 45.20m, DateTime.UtcNow.AddMinutes(-2));
        SetupEtfPriceResponse("SPY", 485.30m, DateTime.UtcNow.AddMinutes(-1));

        // Act
        var result = await _service.EstimateAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeInRange(8m, 80m); // Reasonable VIX bounds
        
        // Verify calculation: 1.45*25.50 + 0.30*12.75 + (-0.20)*45.20 + (-0.05)*485.30 + 8.2
        var expectedVix = 1.45m * 25.50m + 0.30m * 12.75m + (-0.20m) * 45.20m + (-0.05m) * 485.30m + 8.2m;
        result.Should().BeApproximately(expectedVix, 0.01m);
    }

    [Fact]
    public async Task EstimateAsync_WithStaleEtfData_ShouldRejectAndReturnNull()
    {
        // Arrange
        var trainedWeights = new SyntheticVixWeights
        {
            VxxCoefficient = 1.45m,
            UvxyCoefficient = 0.30m,
            SvxyCoefficient = -0.20m,
            SpyCoefficient = -0.05m,
            Intercept = 8.2m,
            TrainedAt = DateTime.UtcNow.AddDays(-1),
            RSquared = 0.85m,
            SampleSize = 252
        };

        var weightsJson = JsonSerializer.Serialize(trainedWeights);
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync(weightsJson);

        // Mock stale ETF price (older than 15 minutes)
        SetupEtfPriceResponse("VXX", 25.50m, DateTime.UtcNow.AddMinutes(-20)); // Stale data

        // Act
        var result = await _service.EstimateAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task EstimateAsync_WithoutTrainedWeights_ShouldUseFallbackWeights()
    {
        // Arrange
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync((RedisValue)RedisValue.Null);

        // Mock ETF price responses
        SetupEtfPriceResponse("VXX", 25.50m, DateTime.UtcNow.AddMinutes(-5));
        SetupEtfPriceResponse("UVXY", 12.75m, DateTime.UtcNow.AddMinutes(-3));
        SetupEtfPriceResponse("SVXY", 45.20m, DateTime.UtcNow.AddMinutes(-2));
        SetupEtfPriceResponse("SPY", 485.30m, DateTime.UtcNow.AddMinutes(-1));

        // Act
        var result = await _service.EstimateAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeInRange(8m, 80m);
        
        // Should use static fallback: 0.7 * VXX + 0.3 * UVXY
        var expectedVix = 0.7m * 25.50m + 0.3m * 12.75m;
        result.Should().BeApproximately(expectedVix, 0.01m);
    }

    [Fact]
    public async Task EstimateAsync_WithOutOfBoundsResult_ShouldReturnNull()
    {
        // Arrange
        var trainedWeights = new SyntheticVixWeights
        {
            VxxCoefficient = 10.0m, // Extreme coefficient to produce out-of-bounds result
            UvxyCoefficient = 0.0m,
            SvxyCoefficient = 0.0m,
            SpyCoefficient = 0.0m,
            Intercept = 0.0m,
            TrainedAt = DateTime.UtcNow.AddDays(-1),
            RSquared = 0.85m,
            SampleSize = 252
        };

        var weightsJson = JsonSerializer.Serialize(trainedWeights);
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync(weightsJson);

        // Mock ETF price responses
        SetupEtfPriceResponse("VXX", 25.50m, DateTime.UtcNow.AddMinutes(-5));
        SetupEtfPriceResponse("UVXY", 12.75m, DateTime.UtcNow.AddMinutes(-3));
        SetupEtfPriceResponse("SVXY", 45.20m, DateTime.UtcNow.AddMinutes(-2));
        SetupEtfPriceResponse("SPY", 485.30m, DateTime.UtcNow.AddMinutes(-1));

        // Act
        var result = await _service.EstimateAsync();

        // Assert
        result.Should().BeNull(); // Should reject out-of-bounds result (10.0 * 25.50 = 255)
    }

    [Fact]
    public async Task GetCurrentWeightsAsync_WithValidWeights_ShouldReturnWeights()
    {
        // Arrange
        var trainedWeights = new SyntheticVixWeights
        {
            VxxCoefficient = 1.45m,
            UvxyCoefficient = 0.30m,
            SvxyCoefficient = -0.20m,
            SpyCoefficient = -0.05m,
            Intercept = 8.2m,
            TrainedAt = DateTime.UtcNow.AddDays(-1),
            RSquared = 0.85m,
            SampleSize = 252
        };

        var weightsJson = JsonSerializer.Serialize(trainedWeights);
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync(weightsJson);

        // Act
        var result = await _service.GetCurrentWeightsAsync();

        // Assert
        result.Should().NotBeNull();
        result!.VxxCoefficient.Should().Be(1.45m);
        result.UvxyCoefficient.Should().Be(0.30m);
        result.SvxyCoefficient.Should().Be(-0.20m);
        result.SpyCoefficient.Should().Be(-0.05m);
        result.Intercept.Should().Be(8.2m);
        result.RSquared.Should().Be(0.85m);
        result.SampleSize.Should().Be(252);
    }

    [Fact]
    public async Task AreWeightsFreshAsync_WithRecentWeights_ShouldReturnTrue()
    {
        // Arrange
        var trainedWeights = new SyntheticVixWeights
        {
            VxxCoefficient = 1.45m,
            UvxyCoefficient = 0.30m,
            SvxyCoefficient = -0.20m,
            SpyCoefficient = -0.05m,
            Intercept = 8.2m,
            TrainedAt = DateTime.UtcNow.AddDays(-3), // Fresh (within 7 days)
            RSquared = 0.85m,
            SampleSize = 252
        };

        var weightsJson = JsonSerializer.Serialize(trainedWeights);
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync(weightsJson);

        // Act
        var result = await _service.AreWeightsFreshAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task AreWeightsFreshAsync_WithStaleWeights_ShouldReturnFalse()
    {
        // Arrange
        var trainedWeights = new SyntheticVixWeights
        {
            VxxCoefficient = 1.45m,
            UvxyCoefficient = 0.30m,
            SvxyCoefficient = -0.20m,
            SpyCoefficient = -0.05m,
            Intercept = 8.2m,
            TrainedAt = DateTime.UtcNow.AddDays(-10), // Stale (older than 7 days)
            RSquared = 0.85m,
            SampleSize = 252
        };

        var weightsJson = JsonSerializer.Serialize(trainedWeights);
        _mockDatabase.Setup(d => d.StringGetAsync("vix:weights", It.IsAny<CommandFlags>()))
            .ReturnsAsync(weightsJson);

        // Act
        var result = await _service.AreWeightsFreshAsync();

        // Assert
        result.Should().BeFalse();
    }

    private void SetupEtfPriceResponse(string symbol, decimal price, DateTime timestamp)
    {
        var response = new
        {
            results = new
            {
                p = price,
                t = ((DateTimeOffset)timestamp).ToUnixTimeMilliseconds()
            }
        };

        var responseJson = JsonSerializer.Serialize(response);
        var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
        {
            Content = new StringContent(responseJson)
        };

        // Note: In a real test, you'd need to properly mock HttpClient
        // This is a simplified version for demonstration
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
