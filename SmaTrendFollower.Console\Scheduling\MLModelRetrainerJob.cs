using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using Quartz;
using SmaTrendFollower.MachineLearning.DataPrep;
using SmaTrendFollower.MachineLearning.ModelTraining;
using SmaTrendFollower.MachineLearning.Prediction;
using SmaTrendFollower.Monitoring;
using SmaTrendFollower.Services;
using StackExchange.Redis;

namespace SmaTrendFollower.Scheduling;

/// <summary>
/// Quartz.NET job for automated ML model retraining.
/// Runs weekly to retrain signal ranking models with fresh data.
/// </summary>
[DisallowConcurrentExecution]
public class MLModelRetrainerJob : IJob
{
    private readonly ILogger<MLModelRetrainerJob> _logger;
    private readonly IServiceProvider _serviceProvider;

    public MLModelRetrainerJob(ILogger<MLModelRetrainerJob> logger, IServiceProvider serviceProvider)
    {
        _logger = logger;
        _serviceProvider = serviceProvider;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        _logger.LogInformation("🤖 Starting ML model retraining job...");

        var startTime = DateTime.UtcNow;
        var success = false;
        string? errorMessage = null;

        // Start metrics tracking
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            using var scope = _serviceProvider.CreateScope();
            var featureExportService = scope.ServiceProvider.GetRequiredService<IFeatureExportService>();
            var signalRanker = scope.ServiceProvider.GetRequiredService<ISignalRanker>();
            var redisService = scope.ServiceProvider.GetService<OptimizedRedisConnectionService>();

            // Step 1: Export fresh training data (last 24 months)
            var fromDate = DateTime.UtcNow.AddMonths(-24);
            var toDate = DateTime.UtcNow.AddDays(-1); // Exclude today to ensure complete data
            var csvPath = Path.Combine("Model", "training_data.csv");

            _logger.LogInformation("Exporting training data from {FromDate:yyyy-MM-dd} to {ToDate:yyyy-MM-dd}...", 
                fromDate, toDate);

            // Validate features before export
            var validationResult = await featureExportService.ValidateFeaturesAsync(fromDate, toDate);
            if (!validationResult.IsValid)
            {
                _logger.LogError("Feature validation failed: {Issues}", string.Join("; ", validationResult.Issues));
                throw new InvalidOperationException($"Feature validation failed: {string.Join("; ", validationResult.Issues)}");
            }

            if (validationResult.Warnings.Any())
            {
                _logger.LogWarning("Feature validation warnings: {Warnings}", string.Join("; ", validationResult.Warnings));
            }

            // Export training data
            await featureExportService.ExportCsvAsync(csvPath, fromDate, toDate);

            // Get export statistics
            var stats = await featureExportService.GetExportStatsAsync(fromDate, toDate);
            _logger.LogInformation("Exported {TotalCount} features (Win rate: {WinRate:P2})", 
                stats.TotalCount, stats.WinRate);

            if (stats.TotalCount < 1000)
            {
                _logger.LogWarning("Insufficient training data: {Count} samples (minimum 1000 recommended)", stats.TotalCount);
                // Continue anyway but log warning
            }

            // Step 2: Train new model
            var modelPath = Path.Combine("Model", "signal_model.zip");
            var backupPath = Path.Combine("Model", $"signal_model_backup_{DateTime.UtcNow:yyyyMMdd_HHmmss}.zip");

            // Backup existing model if it exists
            if (File.Exists(modelPath))
            {
                File.Copy(modelPath, backupPath);
                _logger.LogInformation("Backed up existing model to {BackupPath}", backupPath);
            }

            _logger.LogInformation("Training new ML model...");
            var trainingResult = await TrainSignalRanker.TrainModelAsync(csvPath, modelPath, experimentTimeSeconds: 300);

            if (!trainingResult.Success)
            {
                _logger.LogError("Model training failed: {Error}", trainingResult.ErrorMessage);
                
                // Restore backup if training failed
                if (File.Exists(backupPath))
                {
                    File.Copy(backupPath, modelPath, overwrite: true);
                    _logger.LogInformation("Restored backup model due to training failure");
                }
                
                throw new InvalidOperationException($"Model training failed: {trainingResult.ErrorMessage}");
            }

            _logger.LogInformation("✅ Model training completed successfully!");
            _logger.LogInformation("Model: {ModelName}, Accuracy: {Accuracy:P2}, AUC: {Auc:F3}",
                trainingResult.BestModelName, trainingResult.Accuracy, trainingResult.Auc);

            // Step 3: Update Redis with new model version and invalidate prediction engine
            if (redisService != null)
            {
                try
                {
                    var database = await redisService.GetDatabaseAsync();
                    var modelVersion = File.GetLastWriteTimeUtc(modelPath).Ticks;

                    await database.StringSetAsync("model:signal:version", modelVersion,
                        RedisKeyConstants.RedisKeyTTL.MLModel);

                    // Store model metadata
                    var metadata = new
                    {
                        Version = modelVersion,
                        TrainedAt = DateTime.UtcNow,
                        ModelName = trainingResult.BestModelName,
                        Accuracy = trainingResult.Accuracy,
                        Auc = trainingResult.Auc,
                        TrainingSamples = trainingResult.TrainingSamples
                    };

                    await database.StringSetAsync("model:signal:metadata",
                        System.Text.Json.JsonSerializer.Serialize(metadata),
                        RedisKeyConstants.RedisKeyTTL.MLModel);

                    _logger.LogInformation("Updated Redis with new model version: {Version}", modelVersion);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to update Redis with new model version");
                }
            }

            // Step 4: Reload model in SignalRanker (this will pick up the new version from Redis)
            if (signalRanker is SignalRanker reloadableRanker)
            {
                await reloadableRanker.ReloadModelAsync();
                _logger.LogInformation("Reloaded ML model in SignalRanker");
            }

            // Step 5: Update Prometheus metrics
            MetricsRegistry.MLModelAccuracy.Set(trainingResult.Accuracy);
            MetricsRegistry.MLModelVersion.Set(File.GetLastWriteTimeUtc(modelPath).Ticks);

            // Step 6: Cleanup old backup files (keep last 5)
            await CleanupOldBackupsAsync();

            // Step 7: Log completion metrics
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("🎉 ML model retraining completed successfully in {Duration:mm\\:ss}", duration);

            success = true;
            MetricsRegistry.MLRetrainRuns.WithLabels("success").Inc();

            // Record training duration
            stopwatch.Stop();
            MetricsRegistry.MLRetrainDuration.Observe(stopwatch.Elapsed.TotalSeconds);
        }
        catch (Exception ex)
        {
            errorMessage = ex.Message;
            _logger.LogError(ex, "❌ ML model retraining failed");
            MetricsRegistry.MLRetrainRuns.WithLabels("failure").Inc();
            throw; // Re-throw to mark job as failed
        }
        finally
        {
            // Log job execution summary
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("ML retraining job summary: Success={Success}, Duration={Duration:mm\\:ss}, Error={Error}",
                success, duration, errorMessage ?? "None");

            // Store execution metadata for monitoring
            var executionData = new
            {
                JobName = nameof(MLModelRetrainerJob),
                ExecutedAt = startTime,
                Duration = duration,
                Success = success,
                ErrorMessage = errorMessage
            };

            // TODO: Store in Redis or database for monitoring dashboard
            _logger.LogDebug("Job execution data: {ExecutionData}", 
                System.Text.Json.JsonSerializer.Serialize(executionData));
        }
    }

    /// <summary>
    /// Cleans up old model backup files, keeping only the most recent 5
    /// </summary>
    private async Task CleanupOldBackupsAsync()
    {
        try
        {
            var modelDirectory = "Model";
            if (!Directory.Exists(modelDirectory))
                return;

            var backupFiles = Directory.GetFiles(modelDirectory, "signal_model_backup_*.zip")
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.CreationTime)
                .ToList();

            if (backupFiles.Count <= 5)
                return;

            var filesToDelete = backupFiles.Skip(5);
            foreach (var file in filesToDelete)
            {
                try
                {
                    file.Delete();
                    _logger.LogDebug("Deleted old backup: {FileName}", file.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete backup file: {FileName}", file.Name);
                }
            }

            _logger.LogInformation("Cleaned up {Count} old backup files", filesToDelete.Count());
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during backup cleanup");
        }
    }
}

/// <summary>
/// Extension methods for registering ML retraining job
/// </summary>
public static class MLRetrainerJobExtensions
{
    /// <summary>
    /// Adds ML model retraining job to Quartz scheduler
    /// </summary>
    public static IServiceCollectionQuartzConfigurator AddMLRetrainerJob(
        this IServiceCollectionQuartzConfigurator quartz)
    {
        // Configure the ML retrainer job
        var jobKey = new JobKey("MLModelRetrainer");
        quartz.AddJob<MLModelRetrainerJob>(opts => opts.WithIdentity(jobKey));

        // Schedule weekly on Sunday at 2:00 AM ET (6:00 AM UTC)
        quartz.AddTrigger(t => t
            .ForJob(jobKey)
            .WithIdentity("MLRetrainerTrigger")
            .StartNow()
            .WithCronSchedule("0 0 6 ? * SUN") // Weekly on Sunday at 6:00 AM UTC (2:00 AM ET)
            .WithDescription("Weekly ML model retraining on Sunday at 2:00 AM ET"));

        return quartz;
    }
}

/// <summary>
/// Configuration options for ML retraining job
/// </summary>
public record MLRetrainerConfig(
    int TrainingDataMonths = 24,
    int ExperimentTimeSeconds = 300,
    int MaxBackupFiles = 5,
    string CronSchedule = "0 0 6 ? * SUN", // Sunday 6:00 AM UTC
    bool EnableAutoRetraining = true
);
