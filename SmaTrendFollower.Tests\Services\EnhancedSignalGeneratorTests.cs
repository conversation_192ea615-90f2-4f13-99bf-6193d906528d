using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Diagnostics;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Comprehensive tests for EnhancedSignalGenerator
/// Tests momentum filtering, volatility filtering, parallel processing, and position sizing
/// </summary>
[Trait("Category","Legacy")]
public class EnhancedSignalGeneratorTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IUniverseProvider> _mockUniverseProvider;
    private readonly Mock<ILiveStateStore> _mockLiveStateStore;
    private readonly Mock<IMomentumFilter> _mockMomentumFilter;
    private readonly Mock<IVolatilityFilter> _mockVolatilityFilter;
    private readonly Mock<IPositionSizer> _mockPositionSizer;
    private readonly Mock<ILogger<EnhancedSignalGenerator>> _mockLogger;
    private readonly EnhancedSignalGenerator _generator;

    public EnhancedSignalGeneratorTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockUniverseProvider = new Mock<IUniverseProvider>();
        _mockLiveStateStore = new Mock<ILiveStateStore>();
        _mockMomentumFilter = new Mock<IMomentumFilter>();
        _mockVolatilityFilter = new Mock<IVolatilityFilter>();
        _mockPositionSizer = new Mock<IPositionSizer>();
        _mockLogger = new Mock<ILogger<EnhancedSignalGenerator>>();

        _generator = new EnhancedSignalGenerator(
            _mockMarketDataService.Object,
            _mockUniverseProvider.Object,
            _mockLiveStateStore.Object,
            _mockMomentumFilter.Object,
            _mockVolatilityFilter.Object,
            _mockPositionSizer.Object,
            _mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithValidSymbols_ShouldReturnFilteredSignals()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var topN = 2;

        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(topN);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(topN);
        signalList.Should().OnlyContain(s => s.Price > 0 && s.Atr > 0);
        _mockUniverseProvider.Verify(x => x.GetSymbolsAsync(), Times.Once);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithMomentumFiltering_ShouldApplyMomentumFilter()
    {
        // Arrange
        var symbols = new[] { "STRONG_MOMENTUM", "WEAK_MOMENTUM" };
        SetupSuccessfulDataFetch(symbols);

        var strongMomentum = new MomentumAnalysis(
            Symbol: "STRONG_MOMENTUM",
            CurrentPrice: 150m,
            SixMonthReturn: 0.25m,
            ThreeMonthReturn: 0.15m,
            OneMonthReturn: 0.08m,
            TwoWeekReturn: 0.04m,
            RelativeStrength: 0.90m,
            MomentumScore: 0.85m,
            TrendConsistency: 0.80m,
            VolumeMomentum: 1.2m,
            SMA50: 145m,
            SMA200: 140m,
            AnalysisTime: DateTime.UtcNow
        );

        var weakMomentum = new MomentumAnalysis(
            Symbol: "WEAK_MOMENTUM",
            CurrentPrice: 100m,
            SixMonthReturn: -0.10m,
            ThreeMonthReturn: -0.05m,
            OneMonthReturn: -0.02m,
            TwoWeekReturn: -0.01m,
            RelativeStrength: 0.30m,
            MomentumScore: 0.25m,
            TrendConsistency: 0.40m,
            VolumeMomentum: 0.8m,
            SMA50: 105m,
            SMA200: 110m,
            AnalysisTime: DateTime.UtcNow
        );

        // Setup IsEligible to return true for strong momentum, false for weak momentum
        _mockMomentumFilter.Setup(x => x.IsEligible("STRONG_MOMENTUM", It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(true);
        _mockMomentumFilter.Setup(x => x.IsEligible("WEAK_MOMENTUM", It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(false);

        _mockMomentumFilter.Setup(x => x.AnalyzeMomentum("STRONG_MOMENTUM", It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(strongMomentum);
        _mockMomentumFilter.Setup(x => x.AnalyzeMomentum("WEAK_MOMENTUM", It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(weakMomentum);

        SetupSuccessfulVolatilityFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        _mockMomentumFilter.Verify(x => x.IsEligible(It.IsAny<string>(), It.IsAny<IReadOnlyList<IBar>>()), Times.AtLeast(2));
        _mockMomentumFilter.Verify(x => x.AnalyzeMomentum("STRONG_MOMENTUM", It.IsAny<IReadOnlyList<IBar>>()), Times.AtLeastOnce);
        signalList.Should().Contain(s => s.Symbol == "STRONG_MOMENTUM");
        signalList.Should().NotContain(s => s.Symbol == "WEAK_MOMENTUM"); // Weak momentum should be filtered out
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithVolatilityFiltering_ShouldApplyVolatilityFilter()
    {
        // Arrange
        var symbols = new[] { "LOW_VOL", "HIGH_VOL" };
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulMomentumFiltering();

        var lowVolAnalysis = new SymbolVolatilityAnalysis(
            Symbol: "LOW_VOL",
            Atr14: 2.5m,
            Atr20: 2.8m,
            AtrPercent: 0.015m,
            VolatilityRank: 25m,
            PriceStability: 0.80m,
            IsEligible: true,
            Reason: "Low volatility eligible"
        );

        var highVolAnalysis = new SymbolVolatilityAnalysis(
            Symbol: "HIGH_VOL",
            Atr14: 8.5m,
            Atr20: 9.2m,
            AtrPercent: 0.045m,
            VolatilityRank: 95m,
            PriceStability: 0.20m,
            IsEligible: false,
            Reason: "High volatility rejected"
        );

        // Setup IsEligibleAsync to return true for low vol, false for high vol
        _mockVolatilityFilter.Setup(x => x.IsEligibleAsync("LOW_VOL", It.IsAny<IReadOnlyList<IBar>>()))
            .ReturnsAsync(true);
        _mockVolatilityFilter.Setup(x => x.IsEligibleAsync("HIGH_VOL", It.IsAny<IReadOnlyList<IBar>>()))
            .ReturnsAsync(false);

        _mockVolatilityFilter.Setup(x => x.AnalyzeSymbolVolatility("LOW_VOL", It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(lowVolAnalysis);
        _mockVolatilityFilter.Setup(x => x.AnalyzeSymbolVolatility("HIGH_VOL", It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(highVolAnalysis);

        // Setup market volatility
        var defaultMarketVolatility = new MarketVolatilityAnalysis(
            VixLevel: 20m,
            VixTwentyDayAverage: 18m,
            VixPercentileRank: 50m,
            Regime: MarketVolatilityRegime.Normal,
            IsEligible: true,
            Reason: "Normal market conditions"
        );
        _mockVolatilityFilter.Setup(x => x.GetMarketVolatilityAsync())
            .ReturnsAsync(defaultMarketVolatility);

        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        _mockVolatilityFilter.Verify(x => x.IsEligibleAsync(It.IsAny<string>(), It.IsAny<IReadOnlyList<IBar>>()), Times.AtLeast(2));
        _mockVolatilityFilter.Verify(x => x.AnalyzeSymbolVolatility("LOW_VOL", It.IsAny<IReadOnlyList<IBar>>()), Times.AtLeastOnce);
        signalList.Should().Contain(s => s.Symbol == "LOW_VOL");
        signalList.Should().NotContain(s => s.Symbol == "HIGH_VOL"); // High volatility should be filtered out
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithPositionSizing_ShouldCalculatePositionSizes()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();

        var positionSizing = new DynamicPositionSizing(
            Symbol: "AAPL",
            Shares: 100,
            EntryPrice: 150m,
            StopPrice: 145m,
            PositionValue: 15000m,
            RiskAmount: 500m,
            RiskPercent: 0.01m,
            AtrMultiplier: 2.0m,
            MarketRegime: MarketVolatilityRegime.Normal,
            ConfidenceScore: 0.85m,
            Reason: "Standard position sizing"
        );

        _mockPositionSizer.Setup(x => x.CalculateSizingAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()))
            .ReturnsAsync(positionSizing);

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        _mockPositionSizer.Verify(x => x.CalculateSizingAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()), Times.AtLeast(2));
        signalList.Should().OnlyContain(s => s.Price > 0);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithParallelProcessing_ShouldProcessSymbolsConcurrently()
    {
        // Arrange - Reduced from 20 to 5 symbols for faster execution
        var symbols = Enumerable.Range(1, 5).Select(i => $"SYMBOL{i}").ToArray();
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        var processingTimes = new List<DateTime>();
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .Callback(() => processingTimes.Add(DateTime.UtcNow))
            .ReturnsAsync(CreateMockBarsResponse());

        // Act
        var startTime = DateTime.UtcNow;
        var signals = await _generator.RunAsync(10);
        var endTime = DateTime.UtcNow;

        // Assert
        var totalDuration = endTime - startTime;
        // Parallel processing should be faster than sequential
        totalDuration.Should().BeLessThan(TimeSpan.FromSeconds(10)); // Should complete quickly with parallel processing
        processingTimes.Should().HaveCount(symbols.Length);
    }

    [TestTimeout(TestTimeouts.Integration)]
    [Trait("Category", TestCategories.Performance)]
    [Fact]
    public async Task RunAsync_WithLargeUniverse_ShouldCompleteWithinTimeLimit()
    {
        // Arrange - Test with 200 symbols to verify rate limiting and parallel processing
        var symbols = Enumerable.Range(1, 200).Select(i => $"STOCK{i}").ToArray();
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Add small delay to simulate real API calls
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .Returns(async () =>
            {
                await Task.Delay(10); // Simulate 10ms API call
                return CreateMockBarsResponse();
            });

        // Act
        var stopwatch = Stopwatch.StartNew();
        var signals = await _generator.RunAsync(50);
        stopwatch.Stop();

        // Assert
        var signalList = signals.ToList();
        signalList.Should().NotBeEmpty("Should generate signals from large universe");

        // With 20 concurrent calls and 10ms delay per call, 200 symbols should complete in ~2 seconds
        // (200 symbols / 20 concurrent = 10 batches * 10ms = ~100ms + overhead)
        stopwatch.Elapsed.Should().BeLessThan(TimeSpan.FromSeconds(2),
            "Parallel processing with rate limiting should complete 200 symbols within 2 seconds");

        // Verify we got the expected number of API calls
        _mockMarketDataService.Verify(x => x.GetStockBarsAsync(It.IsAny<string>(), It.IsAny<DateTime>(), It.IsAny<DateTime>()),
            Times.Exactly(symbols.Length));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithBasicTrendFilter_ShouldFilterByTrend()
    {
        // Arrange
        var symbols = new[] { "UPTREND", "DOWNTREND" };
        
        // Setup uptrend symbol (price > SMA50 > SMA200)
        var uptrendBars = CreateBarsWithTrend("UPTREND", currentPrice: 110m, sma50: 105m, sma200: 100m);
        var downtrendBars = CreateBarsWithTrend("DOWNTREND", currentPrice: 90m, sma50: 95m, sma200: 100m);

        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("UPTREND", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(uptrendBars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("DOWNTREND", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(downtrendBars);

        // Setup account for position sizing
        var mockAccount = Mock.Of<IAccount>(a => a.Equity == 100000m);
        _mockMarketDataService.Setup(x => x.GetAccountAsync()).ReturnsAsync(mockAccount);

        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().Contain(s => s.Symbol == "UPTREND");
        signalList.Should().NotContain(s => s.Symbol == "DOWNTREND");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithException_ShouldReturnEmptyList()
    {
        // Arrange
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ThrowsAsync(new Exception("Universe provider error"));

        // Act
        var signals = await _generator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_WithDataFetchFailure_ShouldSkipFailedSymbols()
    {
        // Arrange
        var symbols = new[] { "SUCCESS", "FAILURE" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);
        
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("SUCCESS", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(CreateMockBarsResponse());
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync("FAILURE", It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ThrowsAsync(new Exception("Data fetch failed"));

        // Setup account for position sizing
        var mockAccount = Mock.Of<IAccount>(a => a.Equity == 100000m);
        _mockMarketDataService.Setup(x => x.GetAccountAsync()).ReturnsAsync(mockAccount);

        SetupSuccessfulFiltering();
        SetupSuccessfulPositionSizing();

        // Act
        var signals = await _generator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().Contain(s => s.Symbol == "SUCCESS");
        signalList.Should().NotContain(s => s.Symbol == "FAILURE");
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Unit)]
    [Fact]
    public async Task RunAsync_ShouldRankByConfidenceAndReturn()
    {
        // Arrange
        var symbols = new[] { "HIGH_CONF", "LOW_CONF", "MED_CONF" };
        SetupSuccessfulDataFetch(symbols);
        SetupSuccessfulFiltering();

        var highConfSizing = new DynamicPositionSizing("HIGH_CONF", 100, 150m, 145m, 15000m, 500m, 0.01m, 2.0m, MarketVolatilityRegime.Normal, 0.95m, "High confidence");
        var medConfSizing = new DynamicPositionSizing("MED_CONF", 100, 150m, 145m, 15000m, 500m, 0.01m, 2.0m, MarketVolatilityRegime.Normal, 0.75m, "Medium confidence");
        var lowConfSizing = new DynamicPositionSizing("LOW_CONF", 100, 150m, 145m, 15000m, 500m, 0.01m, 2.0m, MarketVolatilityRegime.Normal, 0.45m, "Low confidence");

        _mockPositionSizer.Setup(x => x.CalculateSizingAsync(It.Is<TradingSignal>(s => s.Symbol == "HIGH_CONF"), It.IsAny<decimal>()))
            .ReturnsAsync(highConfSizing);
        _mockPositionSizer.Setup(x => x.CalculateSizingAsync(It.Is<TradingSignal>(s => s.Symbol == "MED_CONF"), It.IsAny<decimal>()))
            .ReturnsAsync(medConfSizing);
        _mockPositionSizer.Setup(x => x.CalculateSizingAsync(It.Is<TradingSignal>(s => s.Symbol == "LOW_CONF"), It.IsAny<decimal>()))
            .ReturnsAsync(lowConfSizing);

        // Act
        var signals = await _generator.RunAsync(2); // Top 2 only
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(2);
        signalList.First().Symbol.Should().Be("HIGH_CONF"); // Highest confidence first
        signalList.Should().NotContain(s => s.Symbol == "LOW_CONF"); // Lowest confidence filtered out
    }

    private void SetupSuccessfulDataFetch(string[] symbols)
    {
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        foreach (var symbol in symbols)
        {
            _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(CreateMockBarsResponse());
        }

        // Setup account for position sizing
        var mockAccount = Mock.Of<IAccount>(a => a.Equity == 100000m);
        _mockMarketDataService.Setup(x => x.GetAccountAsync()).ReturnsAsync(mockAccount);
    }

    private void SetupSuccessfulFiltering()
    {
        SetupSuccessfulMomentumFiltering();
        SetupSuccessfulVolatilityFiltering();
    }

    private void SetupSuccessfulMomentumFiltering()
    {
        var defaultMomentum = new MomentumAnalysis(
            Symbol: "",
            CurrentPrice: 100m,
            SixMonthReturn: 0.15m,
            ThreeMonthReturn: 0.10m,
            OneMonthReturn: 0.05m,
            TwoWeekReturn: 0.02m,
            RelativeStrength: 0.80m,
            MomentumScore: 0.75m,
            TrendConsistency: 0.70m,
            VolumeMomentum: 1.1m,
            SMA50: 95m,
            SMA200: 90m,
            AnalysisTime: DateTime.UtcNow
        );

        // Setup both IsEligible and AnalyzeMomentum methods
        _mockMomentumFilter.Setup(x => x.IsEligible(It.IsAny<string>(), It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(true);
        _mockMomentumFilter.Setup(x => x.AnalyzeMomentum(It.IsAny<string>(), It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(defaultMomentum);
    }

    private void SetupSuccessfulVolatilityFiltering()
    {
        var defaultVolatility = new SymbolVolatilityAnalysis(
            Symbol: "",
            Atr14: 3.0m,
            Atr20: 3.2m,
            AtrPercent: 0.020m,
            VolatilityRank: 50m,
            PriceStability: 0.70m,
            IsEligible: true,
            Reason: "Normal volatility"
        );

        var defaultMarketVolatility = new MarketVolatilityAnalysis(
            VixLevel: 20m,
            VixTwentyDayAverage: 18m,
            VixPercentileRank: 50m,
            Regime: MarketVolatilityRegime.Normal,
            IsEligible: true,
            Reason: "Normal market conditions"
        );

        // Setup both IsEligibleAsync and AnalyzeSymbolVolatility methods
        _mockVolatilityFilter.Setup(x => x.IsEligibleAsync(It.IsAny<string>(), It.IsAny<IReadOnlyList<IBar>>()))
            .ReturnsAsync(true);
        _mockVolatilityFilter.Setup(x => x.AnalyzeSymbolVolatility(It.IsAny<string>(), It.IsAny<IReadOnlyList<IBar>>()))
            .Returns(defaultVolatility);
        _mockVolatilityFilter.Setup(x => x.GetMarketVolatilityAsync())
            .ReturnsAsync(defaultMarketVolatility);
    }

    private void SetupSuccessfulPositionSizing()
    {
        var defaultSizing = new DynamicPositionSizing(
            Symbol: "DEFAULT",
            Shares: 100,
            EntryPrice: 150m,
            StopPrice: 145m,
            PositionValue: 15000m,
            RiskAmount: 500m,
            RiskPercent: 0.01m,
            AtrMultiplier: 2.0m,
            MarketRegime: MarketVolatilityRegime.Normal,
            ConfidenceScore: 0.80m,
            Reason: "Default sizing"
        );
        _mockPositionSizer.Setup(x => x.CalculateSizingAsync(It.IsAny<TradingSignal>(), It.IsAny<decimal>()))
            .ReturnsAsync(defaultSizing);
    }

    private IPage<IBar> CreateMockBarsResponse()
    {
        var mockBars = new List<IBar>();
        // Reduced from 250 to 50 bars for faster test execution
        for (int i = 0; i < 50; i++)
        {
            // Create an uptrending price series where current price > SMA50 > SMA200
            var basePrice = 100m;
            var trendComponent = i * 1.0m; // Stronger uptrend to compensate for fewer bars
            var price = basePrice + trendComponent;

            var mockBar = Mock.Of<IBar>(b =>
                b.Close == price &&
                b.High == price + 1m &&
                b.Low == price - 1m &&
                b.Open == price &&
                b.Volume == 1000000m &&
                b.TimeUtc == DateTime.UtcNow.AddDays(-50 + i));
            mockBars.Add(mockBar);
        }

        var mockResponse = Mock.Of<IPage<IBar>>(r => r.Items == mockBars);
        return mockResponse;
    }

    private IPage<IBar> CreateBarsWithTrend(string symbol, decimal currentPrice, decimal sma50, decimal sma200)
    {
        var mockBars = new List<IBar>();

        // Create a price series that will result in the desired SMA values
        // For an uptrend: start low and gradually increase to currentPrice
        // For a downtrend: start high and gradually decrease to currentPrice

        var isUptrend = currentPrice > sma50 && sma50 > sma200;

        // Reduced from 250 to 50 bars for faster test execution
        for (int i = 0; i < 50; i++)
        {
            decimal price;
            if (isUptrend)
            {
                // Create an uptrending price series
                var basePrice = 80m; // Start below sma200
                var trendComponent = (currentPrice - basePrice) * i / 49m;
                price = basePrice + trendComponent;
            }
            else
            {
                // Create a downtrending price series
                var basePrice = 120m; // Start above sma200
                var trendComponent = (currentPrice - basePrice) * i / 49m;
                price = basePrice + trendComponent;
            }

            var mockBar = Mock.Of<IBar>(b =>
                b.Close == price &&
                b.High == price + 1m &&
                b.Low == price - 1m &&
                b.Open == price &&
                b.Volume == 1000000m &&
                b.TimeUtc == DateTime.UtcNow.AddDays(-50 + i));
            mockBars.Add(mockBar);
        }

        var mockResponse = Mock.Of<IPage<IBar>>(r => r.Items == mockBars);
        return mockResponse;
    }

    public void Dispose()
    {
        // EnhancedSignalGenerator doesn't implement IDisposable
        // No cleanup needed for this test class
    }
}
