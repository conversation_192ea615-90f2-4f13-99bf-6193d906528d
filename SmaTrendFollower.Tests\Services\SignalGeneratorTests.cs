using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

[Trait("Category","Legacy")]
public class SignalGeneratorTests
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IUniverseProvider> _mockUniverseProvider;
    private readonly Mock<ILogger<SimpleSignalGenerator>> _mockLogger;
    private readonly SimpleSignalGenerator _signalGenerator;

    public SignalGeneratorTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockUniverseProvider = new Mock<IUniverseProvider>();
        _mockLogger = new Mock<ILogger<SimpleSignalGenerator>>();

        _signalGenerator = new SimpleSignalGenerator(
            _mockMarketDataService.Object,
            _mockUniverseProvider.Object,
            _mockLogger.Object);
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    public async Task RunAsync_WithValidSignals_AppliesVolatilityThrottle()
    {
        // Arrange
        var symbols = new[] { "LOW_VOL", "HIGH_VOL" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create signals with different volatility levels
        // Low volatility: 1.5% ATR/Price ratio (should pass < 3%)
        var lowVolatilityBars = CreateBarsWithVolatility("LOW_VOL", price: 100m, volatilityPercent: 1.5m, isUptrend: true);

        // High volatility: 6% ATR/Price ratio (should be filtered out >= 3%)
        var highVolatilityBars = CreateBarsWithVolatility("HIGH_VOL", price: 100m, volatilityPercent: 6.0m, isUptrend: true);

        SetupMarketDataResponse("LOW_VOL", lowVolatilityBars);
        SetupMarketDataResponse("HIGH_VOL", highVolatilityBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert - Verify that volatility throttle is working
        signalList.Should().NotBeEmpty("At least some signals should pass the filters");

        // All returned signals should have ATR/Price < 3%
        foreach (var signal in signalList)
        {
            var atrRatio = signal.Atr / signal.Price;
            atrRatio.Should().BeLessThan(0.03m, $"Signal {signal.Symbol} should have ATR/Price < 3%, but was {atrRatio:P2}");
        }

        // Low volatility should be included
        signalList.Should().Contain(s => s.Symbol == "LOW_VOL", "Low volatility signal should pass the filter");
    }

    [Fact]
    public async Task RunAsync_WithHighVolatilitySignals_FiltersThemOut()
    {
        // Arrange
        var symbols = new[] { "VOLATILE1", "VOLATILE2" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Both signals have ATR/Price > 3%
        var highVolBars1 = CreateBarsWithVolatility("VOLATILE1", price: 100m, volatilityPercent: 5.0m, isUptrend: true);
        var highVolBars2 = CreateBarsWithVolatility("VOLATILE2", price: 100m, volatilityPercent: 4.5m, isUptrend: true);

        SetupMarketDataResponse("VOLATILE1", highVolBars1);
        SetupMarketDataResponse("VOLATILE2", highVolBars2);

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty(); // All signals filtered out due to high volatility
    }

    [Fact]
    public async Task RunAsync_WithSmaFiltering_OnlyIncludesUptrends()
    {
        // Arrange
        var symbols = new[] { "UPTREND", "DOWNTREND", "MIXED" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // UPTREND: price > sma50 > sma200 (should pass)
        var uptrendBars = CreateBarsWithVolatility("UPTREND", price: 100m, volatilityPercent: 2.0m, isUptrend: true);

        // DOWNTREND: price < sma50 < sma200 (should fail)
        var downtrendBars = CreateBarsWithVolatility("DOWNTREND", price: 100m, volatilityPercent: 2.0m, isUptrend: false);

        // MIXED: price > sma50 but price < sma200 (should fail) - create sideways trend
        var mixedBars = CreateMixedTrendBars("MIXED", finalPrice: 100m, volatilityPercent: 2.0m);

        SetupMarketDataResponse("UPTREND", uptrendBars);
        SetupMarketDataResponse("DOWNTREND", downtrendBars);
        SetupMarketDataResponse("MIXED", mixedBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(1);
        signalList.Should().Contain(s => s.Symbol == "UPTREND");
        signalList.Should().NotContain(s => s.Symbol == "DOWNTREND");
        signalList.Should().NotContain(s => s.Symbol == "MIXED");
    }

    [Fact]
    public async Task RunAsync_RanksBySixMonthReturn_ReturnsTopPerformers()
    {
        // Arrange
        var symbols = new[] { "LOW_RETURN", "HIGH_RETURN", "MED_RETURN" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create bars that pass uptrend filter with different returns
        var lowReturnBars = CreateValidUpTrendBars("LOW_RETURN", finalPrice: 105m, sma50: 102m, sma200: 100m, atr: 1.5m);   // 5% return, low volatility
        var highReturnBars = CreateValidUpTrendBars("HIGH_RETURN", finalPrice: 125m, sma50: 120m, sma200: 115m, atr: 2.0m); // 25% return, low volatility
        var medReturnBars = CreateValidUpTrendBars("MED_RETURN", finalPrice: 115m, sma50: 112m, sma200: 108m, atr: 1.8m);   // 15% return, low volatility

        SetupMarketDataResponse("LOW_RETURN", lowReturnBars);
        SetupMarketDataResponse("HIGH_RETURN", highReturnBars);
        SetupMarketDataResponse("MED_RETURN", medReturnBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(3);
        signalList[0].Symbol.Should().Be("HIGH_RETURN"); // Highest return first
        signalList[1].Symbol.Should().Be("MED_RETURN");
        signalList[2].Symbol.Should().Be("LOW_RETURN");
    }

    [Fact]
    public async Task RunAsync_WithTopNLimit_ReturnsCorrectCount()
    {
        // Arrange
        var symbols = new[] { "STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // All pass filters
        foreach (var symbol in symbols)
        {
            var bars = CreateBarsWithVolatility(symbol, price: 100m, volatilityPercent: 2.0m, isUptrend: true);
            SetupMarketDataResponse(symbol, bars);
        }

        // Act
        var signals = await _signalGenerator.RunAsync(3); // Limit to top 3

        // Assert
        signals.Should().HaveCount(3);
    }

    [Fact]
    public async Task RunAsync_WithInsufficientData_SkipsSymbol()
    {
        // Arrange
        var symbols = new[] { "GOOD_DATA", "BAD_DATA" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        var goodBars = CreateBarsWithVolatility("GOOD_DATA", price: 100m, volatilityPercent: 2.0m, isUptrend: true);
        var badBars = CreateShortBarsList(50); // Only 50 bars, need 200 for SMA200

        SetupMarketDataResponse("GOOD_DATA", goodBars);
        SetupMarketDataResponse("BAD_DATA", badBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(1);
        signalList.Should().Contain(s => s.Symbol == "GOOD_DATA");
        signalList.Should().NotContain(s => s.Symbol == "BAD_DATA");
    }

    [Fact]
    public async Task RunAsync_WithZeroOrNegativeValues_FiltersThemOut()
    {
        // Arrange
        var symbols = new[] { "ZERO_PRICE", "VALID" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create bars with zero price (all bars have 0 close price)
        var zeroPriceBars = CreateBarsWithZeroPrice("ZERO_PRICE");

        // Valid bars
        var validBars = CreateBarsWithVolatility("VALID", price: 100m, volatilityPercent: 2.0m, isUptrend: true);

        SetupMarketDataResponse("ZERO_PRICE", zeroPriceBars);
        SetupMarketDataResponse("VALID", validBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert - Only valid should pass (zero price should be filtered)
        signalList.Should().HaveCount(1);
        signalList.Should().Contain(s => s.Symbol == "VALID");
        signalList.Should().NotContain(s => s.Symbol == "ZERO_PRICE");

        // Verify the valid signal has positive price and ATR
        var validSignal = signalList.First(s => s.Symbol == "VALID");
        validSignal.Price.Should().BeGreaterThan(0);
        validSignal.Atr.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task RunAsync_WithException_ReturnsEmptyList()
    {
        // Arrange
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ThrowsAsync(new Exception("Universe provider error"));

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    public async Task RunAsync_ConcentratesCapitalInLeaders_RanksAndLimitsCorrectly()
    {
        // Arrange - Create a smaller universe of 8 symbols for faster testing
        var symbols = new[]
        {
            "LEADER1", "LEADER2",     // Top performers (25%, 22%)
            "STRONG1", "STRONG2",     // Strong performers (18%, 16%)
            "GOOD1", "GOOD2",         // Good performers (12%, 10%)
            "WEAK1", "WEAK2"          // Weak performers (4%, 2%)
        };

        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create bars with specific 6-month returns (all pass SMA and volatility filters)
        var symbolReturns = new Dictionary<string, decimal>
        {
            ["LEADER1"] = 0.25m,   ["LEADER2"] = 0.22m,
            ["STRONG1"] = 0.18m,   ["STRONG2"] = 0.16m,
            ["GOOD1"] = 0.12m,     ["GOOD2"] = 0.10m,
            ["WEAK1"] = 0.04m,     ["WEAK2"] = 0.02m
        };

        foreach (var kvp in symbolReturns)
        {
            var finalPrice = 100m * (1 + kvp.Value); // Calculate final price based on return
            var sma50 = finalPrice * 0.95m; // SMA50 slightly below final price
            var sma200 = finalPrice * 0.90m; // SMA200 below SMA50
            var atr = finalPrice * 0.015m; // 1.5% ATR for low volatility
            var bars = CreateValidUpTrendBars(kvp.Key, finalPrice, sma50, sma200, atr);
            SetupMarketDataResponse(kvp.Key, bars);
        }

        // Act - Request top 5 signals (concentrate capital in leaders)
        var signals = await _signalGenerator.RunAsync(5);
        var signalList = signals.ToList();

        // Assert - Should return exactly 5 signals (capital concentration)
        signalList.Should().HaveCount(5, "Should concentrate capital in top 5 performers only");

        // Verify that leaders are included (top performers)
        signalList.Should().Contain(s => s.Symbol == "LEADER1");
        signalList.Should().Contain(s => s.Symbol == "LEADER2");

        // Verify that weaker performers are excluded (capital concentration)
        signalList.Should().NotContain(s => s.Symbol.StartsWith("WEAK"),
            "Weak performers should be excluded to concentrate capital");

        // Verify all signals have positive returns (top performers only)
        signalList.Should().OnlyContain(s => s.SixMonthReturn > 0,
            "All selected signals should have positive returns");

        // Verify returns are in descending order (most important test for ranking)
        for (int i = 0; i < signalList.Count - 1; i++)
        {
            signalList[i].SixMonthReturn.Should().BeGreaterOrEqualTo(signalList[i + 1].SixMonthReturn,
                $"Signal at index {i} should have higher or equal return than signal at index {i + 1}");
        }

        // Verify that the first signal has higher return than the last (proper ranking)
        signalList[0].SixMonthReturn.Should().BeGreaterThan(signalList[4].SixMonthReturn,
            "The top performer should have significantly higher return than the 5th performer");

        // Verify that LEADER1 is in the top 2 (should be one of the best performers)
        var topTwo = signalList.Take(2).Select(s => s.Symbol).ToList();
        topTwo.Should().Contain("LEADER1", "LEADER1 should be in top 2 performers");
    }

    private void SetupMarketDataResponse(string symbol, List<IBar> bars)
    {
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockResponse.Object);
    }

    private List<IBar> CreateMockBars(string symbol, decimal price, decimal sma50, decimal sma200, decimal atr)
    {
        return CreateMockBarsWithReturn(symbol, price, sma50, sma200, atr, 0.10m); // Default 10% return
    }

    private List<IBar> CreateMockBarsWithReturn(string symbol, decimal price, decimal sma50, decimal sma200, decimal atr, decimal sixMonthReturn)
    {
        var bars = new List<IBar>();
        var startPrice = price / (1 + sixMonthReturn); // Calculate start price for desired return

        // Create 250 bars with realistic price data that will produce expected SMA values
        for (int i = 0; i < 250; i++)
        {
            var mockBar = new Mock<IBar>();
            decimal currentPrice;

            // For the last 50 bars, ensure they average to sma50
            if (i >= 200)
            {
                currentPrice = sma50 + (price - sma50) * (i - 200) / 49m;
            }
            // For bars 50-199, create a trend that leads to sma200
            else if (i >= 50)
            {
                currentPrice = sma200 + (sma50 - sma200) * (i - 50) / 149m;
            }
            // For first 50 bars, create base trend
            else
            {
                currentPrice = startPrice + (sma200 - startPrice) * i / 49m;
            }

            // Add some volatility to create realistic ATR
            var volatilityFactor = atr / price; // Target volatility as percentage
            var dailyRange = currentPrice * (decimal)volatilityFactor;

            mockBar.Setup(x => x.Close).Returns(currentPrice);
            mockBar.Setup(x => x.Open).Returns(currentPrice - dailyRange * 0.2m);
            mockBar.Setup(x => x.High).Returns(currentPrice + dailyRange * 0.6m);
            mockBar.Setup(x => x.Low).Returns(currentPrice - dailyRange * 0.6m);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-249 + i));

            bars.Add(mockBar.Object);
        }

        return bars;
    }

    private List<IBar> CreateShortBarsList(int count)
    {
        var bars = new List<IBar>();
        for (int i = 0; i < count; i++)
        {
            var mockBar = new Mock<IBar>();
            mockBar.Setup(x => x.Close).Returns(100m);
            mockBar.Setup(x => x.Open).Returns(99m);
            mockBar.Setup(x => x.High).Returns(101m);
            mockBar.Setup(x => x.Low).Returns(98m);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-count + i));
            bars.Add(mockBar.Object);
        }
        return bars;
    }

    /// <summary>
    /// Creates bars with specific volatility characteristics and trend direction
    /// </summary>
    /// <param name="symbol">Symbol name</param>
    /// <param name="price">Final price</param>
    /// <param name="volatilityPercent">Target volatility as percentage (e.g., 2.5 for 2.5%)</param>
    /// <param name="isUptrend">True for uptrend, false for downtrend, null for sideways</param>
    /// <returns>List of bars with realistic price data</returns>
    private List<IBar> CreateBarsWithVolatility(string symbol, decimal price, decimal volatilityPercent, bool? isUptrend)
    {
        var bars = new List<IBar>();
        var random = new Random(symbol.GetHashCode()); // Deterministic randomness based on symbol

        // Calculate starting price and ensure proper SMA relationships
        decimal startPrice, sma50Target, sma200Target;

        switch (isUptrend)
        {
            case true:
                // Uptrend: price > sma50 > sma200
                startPrice = price * 0.7m;
                sma200Target = price * 0.85m;
                sma50Target = price * 0.92m;
                break;
            case false:
                // Downtrend: price < sma50 < sma200
                startPrice = price * 1.3m;
                sma200Target = price * 1.15m;
                sma50Target = price * 1.08m;
                break;
            case null:
                // Mixed/Sideways: price > sma50 but price < sma200 (should fail uptrend filter)
                startPrice = price * 0.9m;
                sma200Target = price * 1.05m; // SMA200 above current price
                sma50Target = price * 0.95m;  // SMA50 below current price
                break;
        }

        // Use sufficient bars for SMA200 calculation (need at least 200)
        var barCount = TestConstants.LongSmaBarCount;
        for (int i = 0; i < barCount; i++)
        {
            var mockBar = new Mock<IBar>();
            var progress = (decimal)i / (barCount - 1);

            // Calculate trend component to achieve target SMA relationships
            decimal trendPrice = isUptrend switch
            {
                true => startPrice + (price - startPrice) * progress,
                false => startPrice - (startPrice - price) * progress,
                null => startPrice + (price - startPrice) * progress // Gradual increase but SMA200 will be higher
            };

            // Add volatility
            var dailyVolatility = trendPrice * volatilityPercent / 100m;
            var randomFactor = (decimal)(random.NextDouble() - 0.5) * 2; // -1 to 1

            var open = trendPrice + randomFactor * dailyVolatility * 0.3m;
            var close = trendPrice + randomFactor * dailyVolatility * 0.2m;
            var high = Math.Max(open, close) + dailyVolatility * 0.7m;
            var low = Math.Min(open, close) - dailyVolatility * 0.7m;

            mockBar.Setup(x => x.Close).Returns(close);
            mockBar.Setup(x => x.Open).Returns(open);
            mockBar.Setup(x => x.High).Returns(high);
            mockBar.Setup(x => x.Low).Returns(low);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-(barCount - 1) + i));

            bars.Add(mockBar.Object);
        }

        return bars;
    }

    /// <summary>
    /// Creates bars with specific return characteristics
    /// </summary>
    private List<IBar> CreateBarsWithReturn(string symbol, decimal finalPrice, decimal startPrice, decimal volatilityPercent)
    {
        // Use sufficient bars for SMA200 calculation (need at least 200)
        return TestDataFactory.CreateReturnBars(symbol, startPrice, finalPrice, TestConstants.LongSmaBarCount);
    }

    /// <summary>
    /// Creates bars that pass uptrend filter: currentPrice > sma50 > sma200
    /// </summary>
    private List<IBar> CreateValidUpTrendBars(string symbol, decimal finalPrice, decimal sma50, decimal sma200, decimal atr)
    {
        var bars = new List<IBar>();
        var barCount = TestConstants.LongSmaBarCount;

        // Calculate starting price to achieve desired 6-month return
        // Work backwards from the expected return in the test
        var symbolReturns = new Dictionary<string, decimal>
        {
            ["LEADER1"] = 0.25m,   ["LEADER2"] = 0.22m,
            ["STRONG1"] = 0.18m,   ["STRONG2"] = 0.16m,
            ["GOOD1"] = 0.12m,     ["GOOD2"] = 0.10m,
            ["WEAK1"] = 0.04m,     ["WEAK2"] = 0.02m,
            // For the ranking test
            ["HIGH_RETURN"] = 0.25m,  // 25% return
            ["MED_RETURN"] = 0.15m,   // 15% return
            ["LOW_RETURN"] = 0.05m    // 5% return
        };

        // Get expected return for this symbol, default to 10% if not found
        var expectedReturn = symbolReturns.ContainsKey(symbol) ? symbolReturns[symbol] : 0.10m;

        // Calculate starting price based on expected 6-month return (126 trading days)
        // The GetTotalReturn method looks at bars[count - 126 - 1], so we need to ensure
        // that the price 126 days ago gives us the expected return
        var startPrice = finalPrice / (1 + expectedReturn);
        var sixMonthAgoIndex = barCount - 126 - 1; // Index of the bar 126 days ago

        for (int i = 0; i < barCount; i++)
        {
            var mockBar = new Mock<IBar>();
            decimal currentPrice;

            // Ensure the price at sixMonthAgoIndex gives the correct return
            if (i == sixMonthAgoIndex)
            {
                currentPrice = startPrice; // Exact price for 6-month return calculation
            }
            else if (i < sixMonthAgoIndex)
            {
                // Before the 6-month mark: build up to startPrice
                var progress = (decimal)i / sixMonthAgoIndex;
                currentPrice = startPrice * 0.8m + (startPrice - startPrice * 0.8m) * progress;
            }
            else if (i < 150)
            {
                // Between 6-month mark and recent bars: trend from startPrice to sma50
                var localProgress = (decimal)(i - sixMonthAgoIndex) / (150 - sixMonthAgoIndex);
                currentPrice = startPrice + (sma50 - startPrice) * localProgress;
            }
            else
            {
                // Last 50 bars: trend from sma50 to final price (ensuring final > sma50)
                var localProgress = (decimal)(i - 150) / 50;
                currentPrice = sma50 + (finalPrice - sma50) * localProgress;
            }

            // Add controlled volatility to create realistic ATR
            var volatilityFactor = atr / finalPrice;
            var dailyRange = currentPrice * volatilityFactor;

            mockBar.Setup(x => x.Close).Returns(currentPrice);
            mockBar.Setup(x => x.Open).Returns(currentPrice - dailyRange * 0.1m);
            mockBar.Setup(x => x.High).Returns(currentPrice + dailyRange * 0.5m);
            mockBar.Setup(x => x.Low).Returns(Math.Max(0.01m, currentPrice - dailyRange * 0.5m));
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-(barCount - 1) + i));
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.Symbol).Returns(symbol);

            bars.Add(mockBar.Object);
        }

        return bars;
    }

    /// <summary>
    /// Creates bars with zero price for testing price filtering
    /// </summary>
    private List<IBar> CreateBarsWithZeroPrice(string symbol)
    {
        // Use sufficient bars for SMA200 calculation but with zero price for filter testing
        return TestDataFactory.CreateMinimalBars(symbol, TestConstants.LongSmaBarCount, basePrice: 0m);
    }

    /// <summary>
    /// Creates bars for mixed trend that should fail uptrend filter: price > sma50 but price < sma200
    /// </summary>
    private List<IBar> CreateMixedTrendBars(string symbol, decimal finalPrice, decimal volatilityPercent)
    {
        var bars = new List<IBar>();
        var barCount = TestConstants.LongSmaBarCount;
        var random = new Random(symbol.GetHashCode());

        // For mixed trend: ensure price < sma200 but price > sma50
        // This should fail the uptrend filter (price > sma50 && price > sma200)
        var sma200Target = finalPrice * 1.10m; // SMA200 10% above final price
        var sma50Target = finalPrice * 0.95m;  // SMA50 5% below final price

        // Start high to create higher SMA200
        var startPrice = finalPrice * 1.20m;

        for (int i = 0; i < barCount; i++)
        {
            var mockBar = new Mock<IBar>();
            var progress = (decimal)i / (barCount - 1);

            decimal currentPrice;
            if (i < 100)
            {
                // First 100 bars: high prices to build up SMA200
                currentPrice = startPrice - (startPrice - sma200Target) * progress * 0.5m;
            }
            else if (i < 200)
            {
                // Next 100 bars: decline to around SMA50 level
                var localProgress = (decimal)(i - 100) / 100;
                currentPrice = sma200Target - (sma200Target - sma50Target) * localProgress;
            }
            else
            {
                // Last 50 bars: settle around final price (between sma50 and sma200)
                var localProgress = (decimal)(i - 200) / 50;
                currentPrice = sma50Target + (finalPrice - sma50Target) * localProgress;
            }

            // Add volatility
            var dailyVolatility = currentPrice * volatilityPercent / 100m;
            var randomFactor = (decimal)(random.NextDouble() - 0.5) * 2;

            var open = currentPrice + randomFactor * dailyVolatility * 0.3m;
            var close = currentPrice + randomFactor * dailyVolatility * 0.2m;
            var high = Math.Max(open, close) + dailyVolatility * 0.7m;
            var low = Math.Min(open, close) - dailyVolatility * 0.7m;

            mockBar.Setup(x => x.Close).Returns(close);
            mockBar.Setup(x => x.Open).Returns(open);
            mockBar.Setup(x => x.High).Returns(high);
            mockBar.Setup(x => x.Low).Returns(low);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-(barCount - 1) + i));
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.Symbol).Returns(symbol);

            bars.Add(mockBar.Object);
        }

        return bars;
    }
}
