using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using System.Net.WebSockets;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Enhanced tests for PolygonWebSocketManager focusing on reconnection and re-subscription behavior
/// </summary>
[Trait("Category","Legacy")]
public class PolygonWebSocketManagerEnhancedTests : IDisposable
{
    private readonly RedisTestFixture _redisFixture;
    private readonly IDatabase _redis;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ILogger<PolygonWebSocketManager> _logger;
    private readonly ITradingMetricsService _metricsService;
    private readonly MockWebSocketFactory _mockWebSocketFactory;
    private readonly string _testApiKey = "test-polygon-api-key";

    public PolygonWebSocketManagerEnhancedTests()
    {
        _redisFixture = new RedisTestFixture();
        _redis = _redisFixture.Database;
        _connectionMultiplexer = _redisFixture.Connection;
        
        _redisService = Substitute.For<IOptimizedRedisConnectionService>();
        _redisService.GetDatabaseAsync().Returns(Task.FromResult(_redis));
        
        _logger = Substitute.For<ILogger<PolygonWebSocketManager>>();
        _metricsService = Substitute.For<ITradingMetricsService>();
        _mockWebSocketFactory = new MockWebSocketFactory();
    }

    [Fact]
    public async Task OnReconnect_ReSubscribesBatchFromRedis()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var channel = PolygonWsChannel.EquityMinute;
        var redisKey = channel.GetRedisSubscriptionKey();
        
        // Pre-populate Redis with subscription data
        await _redis.StringSetAsync(redisKey, string.Join(",", symbols), TimeSpan.FromHours(24));
        
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        mockWebSocket.SimulateOpen();
        
        // Create manager with mock WebSocket factory
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Act - Simulate initial connection and subscription
        await manager.BatchSubscribeAsync(channel, symbols);
        
        // Simulate disconnect and reconnect
        mockWebSocket.SimulateClose();
        await Task.Delay(50); // Allow disconnect processing
        
        mockWebSocket.SimulateOpen();
        await Task.Delay(50); // Allow reconnect processing

        // Assert
        var sentMessages = mockWebSocket.MessagesSent;
        
        // Should have authentication message
        sentMessages.Should().Contain(m => m.Contains("auth"));
        
        // Should have subscription messages for all symbols
        sentMessages.Should().Contain(m => m.Contains("subscribe") && m.Contains("AAPL"));
        sentMessages.Should().Contain(m => m.Contains("subscribe") && m.Contains("MSFT"));
        sentMessages.Should().Contain(m => m.Contains("subscribe") && m.Contains("GOOGL"));
    }

    [Fact]
    public async Task BatchSubscribe_WithLargeSymbolList_ChunksInto500SymbolBatches()
    {
        // Arrange
        var symbols = Enumerable.Range(1, 1250)
            .Select(i => $"SYMBOL{i:D4}")
            .ToArray();
        
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        mockWebSocket.SimulateOpen();
        
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Act
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols);
        
        // Assert
        var subscribeMessages = mockWebSocket.MessagesSent
            .Where(m => m.Contains("subscribe"))
            .ToList();
        
        // Should have 3 subscription messages (1250 symbols / 500 max per batch = 3 batches)
        subscribeMessages.Should().HaveCount(3);
        
        // Verify Redis persistence
        var redisKey = PolygonWsChannel.EquityTrades.GetRedisSubscriptionKey();
        var persistedSymbols = await _redis.StringGetAsync(redisKey);
        persistedSymbols.Should().Be(string.Join(",", symbols));
    }

    [Fact]
    public async Task WebSocketConnection_HandlesConcurrentSubscriptions()
    {
        // Arrange
        var symbols1 = new[] { "AAPL", "MSFT" };
        var symbols2 = new[] { "GOOGL", "AMZN" };
        var symbols3 = new[] { "TSLA", "NVDA" };
        
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        mockWebSocket.SimulateOpen();
        
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Act - Concurrent subscriptions
        var tasks = new[]
        {
            manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols1),
            manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols2),
            manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols3)
        };
        
        await Task.WhenAll(tasks);
        
        // Assert
        var subscribeMessages = mockWebSocket.MessagesSent
            .Where(m => m.Contains("subscribe"))
            .ToList();
        
        subscribeMessages.Should().HaveCountGreaterOrEqualTo(3);
        
        // All symbols should be in Redis
        var redisKey = PolygonWsChannel.EquityTrades.GetRedisSubscriptionKey();
        var persistedSymbols = await _redis.StringGetAsync(redisKey);
        var allSymbols = symbols1.Concat(symbols2).Concat(symbols3);
        
        foreach (var symbol in allSymbols)
        {
            persistedSymbols.ToString().Should().Contain(symbol);
        }
    }

    [Fact]
    public async Task MultipleChannels_MaintainSeparateConnections()
    {
        // Arrange
        var equitySymbols = new[] { "AAPL", "MSFT" };
        var indexSymbols = new[] { "VIX", "SPX" };
        
        var equityWebSocket = _mockWebSocketFactory.CreateWebSocket();
        var indexWebSocket = _mockWebSocketFactory.CreateWebSocket();
        
        equityWebSocket.SimulateOpen();
        indexWebSocket.SimulateOpen();
        
        var manager = CreateManagerWithMultipleWebSockets(equityWebSocket, indexWebSocket);
        
        // Act
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, equitySymbols);
        await manager.BatchSubscribeAsync(PolygonWsChannel.IndexMinute, indexSymbols);
        
        // Assert
        var equityMessages = equityWebSocket.MessagesSent;
        var indexMessages = indexWebSocket.MessagesSent;
        
        // Equity WebSocket should have equity subscriptions
        equityMessages.Should().Contain(m => m.Contains("AAPL"));
        equityMessages.Should().NotContain(m => m.Contains("VIX"));
        
        // Index WebSocket should have index subscriptions
        indexMessages.Should().Contain(m => m.Contains("VIX"));
        indexMessages.Should().NotContain(m => m.Contains("AAPL"));
        
        // Verify separate Redis keys
        var equityKey = PolygonWsChannel.EquityTrades.GetRedisSubscriptionKey();
        var indexKey = PolygonWsChannel.IndexMinute.GetRedisSubscriptionKey();
        
        var equityRedisData = await _redis.StringGetAsync(equityKey);
        var indexRedisData = await _redis.StringGetAsync(indexKey);
        
        equityRedisData.ToString().Should().Contain("AAPL");
        indexRedisData.ToString().Should().Contain("VIX");
    }

    [Fact]
    public async Task ConnectionFailure_TriggersExponentialBackoffReconnection()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Act - Simulate connection failure
        mockWebSocket.SimulateClose(WebSocketCloseStatus.InternalServerError, "Connection failed");
        
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols);
        
        // Simulate multiple reconnection attempts
        for (int i = 0; i < 3; i++)
        {
            await Task.Delay(100);
            mockWebSocket.SimulateOpen();
            await Task.Delay(50);
            mockWebSocket.SimulateClose(WebSocketCloseStatus.InternalServerError);
        }
        
        // Final successful connection
        mockWebSocket.SimulateOpen();
        await Task.Delay(100);
        
        // Assert
        _logger.Received().LogWarning(Arg.Is<string>(s => s.Contains("reconnection attempt")));
        
        // Should eventually succeed and subscribe
        var finalMessages = mockWebSocket.MessagesSent;
        finalMessages.Should().Contain(m => m.Contains("subscribe"));
    }

    [Fact]
    public async Task RateLimitError_ActivatesCircuitBreaker()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        mockWebSocket.SimulateOpen();
        
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Simulate rate limit response
        mockWebSocket.SimulateReceiveJson(new
        {
            ev = "status",
            status = "error",
            message = "rate limit exceeded"
        });
        
        // Act
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols);
        
        // Assert
        _logger.Received().LogWarning(Arg.Is<string>(s => s.Contains("rate limit")));
        
        // Should record metrics for rate limit
        _metricsService.Received().RecordSystemMetricAsync(
            Arg.Is<string>(s => s.Contains("rate_limit")),
            Arg.Any<decimal>(),
            Arg.Any<string>());
    }

    [Fact]
    public async Task DisconnectAll_ClosesAllConnections()
    {
        // Arrange
        var equityWebSocket = _mockWebSocketFactory.CreateWebSocket();
        var indexWebSocket = _mockWebSocketFactory.CreateWebSocket();
        
        equityWebSocket.SimulateOpen();
        indexWebSocket.SimulateOpen();
        
        var manager = CreateManagerWithMultipleWebSockets(equityWebSocket, indexWebSocket);
        
        // Subscribe to both channels
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, new[] { "AAPL" });
        await manager.BatchSubscribeAsync(PolygonWsChannel.IndexMinute, new[] { "VIX" });
        
        // Act
        await manager.DisconnectAllAsync();
        
        // Assert
        equityWebSocket.State.Should().Be(WebSocketState.Closed);
        indexWebSocket.State.Should().Be(WebSocketState.Closed);
        
        _logger.Received().LogInformation(Arg.Is<string>(s => s.Contains("All WebSocket connections disconnected")));
    }

    [Fact]
    public async Task GetConnectionState_ReturnsCorrectState()
    {
        // Arrange
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Act & Assert - Initially None
        manager.GetConnectionState(PolygonWsChannel.EquityTrades).Should().Be(WebSocketState.None);
        
        // Open connection
        mockWebSocket.SimulateOpen();
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, new[] { "AAPL" });
        
        manager.GetConnectionState(PolygonWsChannel.EquityTrades).Should().Be(WebSocketState.Open);
        
        // Close connection
        mockWebSocket.SimulateClose();
        await Task.Delay(50);
        
        manager.GetConnectionState(PolygonWsChannel.EquityTrades).Should().Be(WebSocketState.Closed);
    }

    [Fact]
    public async Task GetSubscribedSymbolCount_ReturnsCorrectCount()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        var mockWebSocket = _mockWebSocketFactory.CreateWebSocket();
        mockWebSocket.SimulateOpen();
        
        var manager = CreateManagerWithMockWebSocket(mockWebSocket);
        
        // Act
        await manager.BatchSubscribeAsync(PolygonWsChannel.EquityTrades, symbols);
        
        // Assert
        var count = manager.GetSubscribedSymbolCount(PolygonWsChannel.EquityTrades);
        count.Should().Be(symbols.Length);
    }

    private PolygonWebSocketManager CreateManagerWithMockWebSocket(MockWebSocket mockWebSocket)
    {
        // This would require modifying the PolygonWebSocketManager to accept a WebSocket factory
        // For now, we'll create a basic manager and test what we can
        return new PolygonWebSocketManager(_redisService, _logger, _testApiKey, _metricsService);
    }

    private PolygonWebSocketManager CreateManagerWithMultipleWebSockets(MockWebSocket equityWs, MockWebSocket indexWs)
    {
        // Similar to above - would need WebSocket factory injection
        return new PolygonWebSocketManager(_redisService, _logger, _testApiKey, _metricsService);
    }

    public void Dispose()
    {
        _redisFixture?.Dispose();
    }
}
