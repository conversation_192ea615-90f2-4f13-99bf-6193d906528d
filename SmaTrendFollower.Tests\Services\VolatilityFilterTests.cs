using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

[TestTimeout(TestTimeouts.Unit)]
[Trait("Category", TestCategories.Unit)]
[Trait("Category","Legacy")]
public class VolatilityFilterTests : IDisposable
{
    private readonly Mock<IVIXResolverService> _mockVixResolverService;
    private readonly Mock<ILogger<VolatilityFilter>> _mockLogger;
    private readonly VolatilityFilter _volatilityFilter;

    public VolatilityFilterTests()
    {
        _mockVixResolverService = new Mock<IVIXResolverService>();
        _mockLogger = new Mock<ILogger<VolatilityFilter>>();
        
        _volatilityFilter = new VolatilityFilter(
            _mockVixResolverService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task GetMarketVolatilityAsync_WithValidVixData_ShouldReturnAnalysis()
    {
        // Arrange
        var vixResult = new VixResolverResult
        {
            VixValue = 18.5m,
            IsFresh = true,
            Source = "Polygon WebSocket",
            FallbackLevel = VixFallbackLevel.PolygonWebSocket,
            Quality = VixDataQuality.NearRealTime,
            RetrievedAt = DateTime.UtcNow,
            ErrorMessage = string.Empty
        };

        _mockVixResolverService
            .Setup(x => x.GetVixWithFreshnessAsync(default(TimeSpan), default(CancellationToken)))
            .ReturnsAsync(vixResult);

        // Act
        var result = await _volatilityFilter.GetMarketVolatilityAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixLevel.Should().Be(18.5m);
        result.IsEligible.Should().BeTrue();
        result.Regime.Should().Be(MarketVolatilityRegime.Normal);
        result.Reason.Should().Be("Eligible");
    }

    [Fact]
    public async Task GetMarketVolatilityAsync_WithHighVix_ShouldBlockTrading()
    {
        // Arrange
        var vixResult = new VixResolverResult
        {
            VixValue = 35.0m, // High VIX
            IsFresh = true,
            Source = "Polygon WebSocket",
            FallbackLevel = VixFallbackLevel.PolygonWebSocket,
            Quality = VixDataQuality.NearRealTime,
            RetrievedAt = DateTime.UtcNow,
            ErrorMessage = string.Empty
        };

        _mockVixResolverService
            .Setup(x => x.GetVixWithFreshnessAsync(default(TimeSpan), default(CancellationToken)))
            .ReturnsAsync(vixResult);

        // Act
        var result = await _volatilityFilter.GetMarketVolatilityAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixLevel.Should().Be(35.0m);
        result.IsEligible.Should().BeFalse();
        result.Regime.Should().Be(MarketVolatilityRegime.Crisis);
        result.Reason.Should().Contain("VIX too high");
    }

    [Fact]
    public async Task GetMarketVolatilityAsync_WithNoVixData_ShouldBlockTrading()
    {
        // Arrange
        var vixResult = new VixResolverResult
        {
            VixValue = null,
            IsFresh = false,
            Source = string.Empty,
            FallbackLevel = VixFallbackLevel.TradingHalt,
            Quality = VixDataQuality.Unknown,
            RetrievedAt = DateTime.UtcNow,
            ErrorMessage = "All VIX sources failed"
        };

        _mockVixResolverService
            .Setup(x => x.GetVixWithFreshnessAsync(default(TimeSpan), default(CancellationToken)))
            .ReturnsAsync(vixResult);

        // Act
        var result = await _volatilityFilter.GetMarketVolatilityAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixLevel.Should().Be(25.0m); // Conservative estimate
        result.IsEligible.Should().BeFalse(); // Conservative: block trading
        result.Regime.Should().Be(MarketVolatilityRegime.Elevated);
        result.Reason.Should().Contain("All VIX sources failed");
    }

    [Fact]
    public async Task GetMarketVolatilityAsync_WithException_ShouldBlockTrading()
    {
        // Arrange
        _mockVixResolverService
            .Setup(x => x.GetVixWithFreshnessAsync(default(TimeSpan), default(CancellationToken)))
            .ThrowsAsync(new Exception("VIX resolver error"));

        // Act
        var result = await _volatilityFilter.GetMarketVolatilityAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixLevel.Should().Be(30.0m); // High VIX estimate for safety
        result.IsEligible.Should().BeFalse(); // Block trading on errors
        result.Regime.Should().Be(MarketVolatilityRegime.Crisis);
        result.Reason.Should().Contain("VIX analysis error");
    }

    [Theory]
    [InlineData(12.0, MarketVolatilityRegime.Low)]
    [InlineData(18.0, MarketVolatilityRegime.Normal)]
    [InlineData(25.0, MarketVolatilityRegime.Elevated)]
    [InlineData(35.0, MarketVolatilityRegime.Crisis)]
    public async Task GetMarketVolatilityAsync_WithDifferentVixLevels_ShouldReturnCorrectRegime(
        decimal vixValue, MarketVolatilityRegime expectedRegime)
    {
        // Arrange
        var vixResult = new VixResolverResult
        {
            VixValue = vixValue,
            IsFresh = true,
            Source = "Test",
            FallbackLevel = VixFallbackLevel.PolygonWebSocket,
            Quality = VixDataQuality.NearRealTime,
            RetrievedAt = DateTime.UtcNow,
            ErrorMessage = string.Empty
        };

        _mockVixResolverService
            .Setup(x => x.GetVixWithFreshnessAsync(default(TimeSpan), default(CancellationToken)))
            .ReturnsAsync(vixResult);

        // Act
        var result = await _volatilityFilter.GetMarketVolatilityAsync();

        // Assert
        result.Should().NotBeNull();
        result.VixLevel.Should().Be(vixValue);
        result.Regime.Should().Be(expectedRegime);
    }

    public void Dispose()
    {
        _volatilityFilter?.Dispose();
    }
}
