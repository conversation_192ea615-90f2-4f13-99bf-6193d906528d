using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for QuoteVolatilityGuard service
/// Tests spread volatility detection, halt mechanisms, and Redis integration
/// </summary>
[Trait("Category","Legacy")]
public class QuoteVolatilityGuardTests : IDisposable
{
    private readonly Mock<IConnectionMultiplexer> _mockConnectionMultiplexer;
    private readonly Mock<IDatabase> _mockDatabase;
    private readonly Mock<ILogger<QuoteVolatilityGuard>> _mockLogger;
    private readonly QuoteVolatilityGuard _guard;

    public QuoteVolatilityGuardTests()
    {
        _mockConnectionMultiplexer = new Mock<IConnectionMultiplexer>();
        _mockDatabase = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<QuoteVolatilityGuard>>();

        _mockConnectionMultiplexer.Setup(x => x.GetDatabase(It.IsAny<int>(), It.IsAny<object>()))
            .Returns(_mockDatabase.Object);

        _guard = new QuoteVolatilityGuard(_mockConnectionMultiplexer.Object, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldInitialize()
    {
        // Arrange & Act
        var guard = new QuoteVolatilityGuard(_mockConnectionMultiplexer.Object, _mockLogger.Object);

        // Assert
        guard.Should().NotBeNull();
    }

    [Fact]
    public void Constructor_WithNullConnectionMultiplexer_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            new QuoteVolatilityGuard(null!, _mockLogger.Object));
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => 
            new QuoteVolatilityGuard(_mockConnectionMultiplexer.Object, null!));
    }

    [Theory]
    [InlineData("", 100.0, 100.5)] // Empty symbol
    [InlineData("AAPL", 0, 100.5)] // Invalid bid
    [InlineData("AAPL", 100.0, 0)] // Invalid ask
    [InlineData("AAPL", 100.5, 100.0)] // Ask <= bid
    public void OnQuote_WithInvalidInputs_ShouldNotProcessQuote(string symbol, decimal bid, decimal ask)
    {
        // Act
        _guard.OnQuote(symbol, bid, ask);

        // Assert - No Redis calls should be made for invalid inputs
        _mockDatabase.Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), 
            It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Never);
    }

    [Fact]
    public void OnQuote_WithValidInputs_ShouldProcessQuote()
    {
        // Arrange
        const string symbol = "AAPL";
        const decimal bid = 150.00m;
        const decimal ask = 150.10m;

        // Act
        _guard.OnQuote(symbol, bid, ask);

        // Assert - Should not halt on first quote (insufficient data)
        _mockDatabase.Verify(x => x.StringSetAsync(It.IsAny<RedisKey>(), It.IsAny<RedisValue>(), 
            It.IsAny<TimeSpan?>(), It.IsAny<When>(), It.IsAny<CommandFlags>()), Times.Never);
    }

    [Fact]
    public void OnQuote_WithVolatileSpread_ShouldTriggerHalt()
    {
        // Arrange
        const string symbol = "TEST";
        const decimal basePrice = 100.00m;
        
        // Setup Redis to return success for halt key setting
        _mockDatabase.Setup(x => x.StringSetAsync(
            It.Is<RedisKey>(k => k == $"halt:{symbol}"),
            It.Is<RedisValue>(v => v == "volatility"),
            It.Is<TimeSpan?>(t => t == TimeSpan.FromMinutes(2)),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()))
            .ReturnsAsync(true);

        // Feed normal spreads first to establish baseline
        for (int i = 0; i < 50; i++)
        {
            var normalBid = basePrice - 0.05m;
            var normalAsk = basePrice + 0.05m;
            _guard.OnQuote(symbol, normalBid, normalAsk);
        }

        // Act - Feed extremely wide spread that should trigger halt
        var volatileBid = basePrice - 2.00m; // Very wide spread
        var volatileAsk = basePrice + 2.00m;
        _guard.OnQuote(symbol, volatileBid, volatileAsk);

        // Allow async halt operation to complete
        Thread.Sleep(100);

        // Assert - Should have set halt key
        _mockDatabase.Verify(x => x.StringSetAsync(
            It.Is<RedisKey>(k => k == $"halt:{symbol}"),
            It.Is<RedisValue>(v => v == "volatility"),
            It.Is<TimeSpan?>(t => t == TimeSpan.FromMinutes(2)),
            It.IsAny<When>(),
            It.IsAny<CommandFlags>()), Times.Once);
    }

    [Fact]
    public async Task IsTradingHaltedAsync_WithHaltedSymbol_ShouldReturnTrue()
    {
        // Arrange
        const string symbol = "AAPL";
        _mockDatabase.Setup(x => x.StringGetAsync(It.Is<RedisKey>(k => k == $"halt:{symbol}"), It.IsAny<CommandFlags>()))
            .ReturnsAsync(new RedisValue("volatility"));

        // Act
        var result = await _guard.IsTradingHaltedAsync(symbol);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsTradingHaltedAsync_WithNonHaltedSymbol_ShouldReturnFalse()
    {
        // Arrange
        const string symbol = "AAPL";
        _mockDatabase.Setup(x => x.StringGetAsync(It.Is<RedisKey>(k => k == $"halt:{symbol}"), It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        // Act
        var result = await _guard.IsTradingHaltedAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsTradingHaltedAsync_WithDifferentHaltValue_ShouldReturnFalse()
    {
        // Arrange - Simulate halt set by AnomalyDetectorService (value = "1")
        const string symbol = "AAPL";
        _mockDatabase.Setup(x => x.StringGetAsync(It.Is<RedisKey>(k => k == $"halt:{symbol}"), It.IsAny<CommandFlags>()))
            .ReturnsAsync(new RedisValue("1")); // AnomalyDetectorService value

        // Act
        var result = await _guard.IsTradingHaltedAsync(symbol);

        // Assert
        result.Should().BeFalse(); // QuoteVolatilityGuard only recognizes "volatility" value
    }

    [Fact]
    public async Task IsTradingHaltedAsync_WithEmptySymbol_ShouldReturnFalse()
    {
        // Act
        var result = await _guard.IsTradingHaltedAsync("");

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsTradingHaltedAsync_WithRedisException_ShouldReturnFalse()
    {
        // Arrange
        const string symbol = "AAPL";
        _mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
            .ThrowsAsync(new RedisException("Redis connection failed"));

        // Act
        var result = await _guard.IsTradingHaltedAsync(symbol);

        // Assert
        result.Should().BeFalse(); // Should default to allowing trading on Redis failure
    }

    [Fact]
    public void GetSpreadStats_WithExistingSymbol_ShouldReturnStats()
    {
        // Arrange
        const string symbol = "AAPL";
        _guard.OnQuote(symbol, 100.00m, 100.10m);

        // Act
        var stats = _guard.GetSpreadStats(symbol);

        // Assert
        stats.Should().NotBeNull();
        stats.Value.Count.Should().Be(1);
        stats.Value.WindowSize.Should().Be(120);
    }

    [Fact]
    public void GetSpreadStats_WithNonExistentSymbol_ShouldReturnNull()
    {
        // Act
        var stats = _guard.GetSpreadStats("NONEXISTENT");

        // Assert
        stats.Should().BeNull();
    }

    [Fact]
    public void GetSpreadStats_WithEmptySymbol_ShouldReturnNull()
    {
        // Act
        var stats = _guard.GetSpreadStats("");

        // Assert
        stats.Should().BeNull();
    }

    [Fact]
    public void ClearSpreadStats_WithExistingSymbol_ShouldClearStats()
    {
        // Arrange
        const string symbol = "AAPL";
        _guard.OnQuote(symbol, 100.00m, 100.10m);
        _guard.GetSpreadStats(symbol).Should().NotBeNull();

        // Act
        _guard.ClearSpreadStats(symbol);

        // Assert
        _guard.GetSpreadStats(symbol).Should().BeNull();
    }

    [Fact]
    public void ClearSpreadStats_WithNonExistentSymbol_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _guard.ClearSpreadStats("NONEXISTENT");
        action.Should().NotThrow();
    }

    [Fact]
    public void OnQuote_CalculatesSpreadPercentageCorrectly()
    {
        // Arrange
        const string symbol = "TEST";
        const decimal bid = 100.00m;
        const decimal ask = 100.20m;
        // Expected spread percentage = (ask - bid) / midPrice = 0.20 / 100.10 ≈ 0.001998

        // Act
        _guard.OnQuote(symbol, bid, ask);

        // Assert
        var stats = _guard.GetSpreadStats(symbol);
        stats.Should().NotBeNull();
        stats.Value.Count.Should().Be(1);
        // The exact spread value is stored internally and tested through behavior
    }

    public void Dispose()
    {
        _guard?.Dispose();
    }
}
