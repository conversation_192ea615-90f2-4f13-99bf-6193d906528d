using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using SmaTrendFollower.Tests.TestHelpers;

namespace SmaTrendFollower.Tests.Services;

[Trait("Category","Legacy")]
public class AlpacaRateLimitHelperTests
{
    private readonly Mock<ILogger<AlpacaRateLimitHelper>> _mockLogger;
    private readonly AlpacaRateLimitHelper _helper;

    public AlpacaRateLimitHelperTests()
    {
        _mockLogger = new Mock<ILogger<AlpacaRateLimitHelper>>();
        _helper = new AlpacaRateLimitHelper(_mockLogger.Object);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ExecuteAsync_ShouldExecuteSuccessfulOperation()
    {
        // Arrange
        var expectedResult = "success";

        // Act
        var result = await _helper.ExecuteAsync(async () =>
        {
            // Remove Task.Delay for faster test execution - rate limiting doesn't require actual delay in tests
            await Task.CompletedTask;
            return expectedResult;
        }, "TestOperation");

        // Assert
        result.Should().Be(expectedResult);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ExecuteAsync_ShouldRetryOnRateLimitException()
    {
        // Arrange
        var callCount = 0;
        var expectedResult = "success";

        // Act
        var result = await _helper.ExecuteAsync(() =>
        {
            callCount++;
            if (callCount == 1)
            {
                throw new Exception("TooManyRequests");
            }
            return Task.FromResult(expectedResult);
        }, "TestOperation");

        // Assert
        result.Should().Be(expectedResult);
        callCount.Should().Be(2);
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ExecuteAsync_WithoutReturnValue_ShouldExecuteSuccessfully()
    {
        // Arrange
        var executed = false;

        // Act
        await _helper.ExecuteAsync(async () =>
        {
            // Remove Task.Delay for faster test execution
            await Task.CompletedTask;
            executed = true;
        }, "TestOperation");

        // Assert
        executed.Should().BeTrue();
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public async Task ExecuteAsync_ShouldHandleConcurrentRequests()
    {
        // Arrange
        var tasks = new List<Task<string>>();
        
        // Act - Reduced from 10 to 3 concurrent operations to avoid triggering rate limits
        for (int i = 0; i < 3; i++)
        {
            var taskIndex = i;
            tasks.Add(_helper.ExecuteAsync(async () =>
            {
                // Remove Task.Delay for faster test execution
                await Task.CompletedTask;
                return $"result-{taskIndex}";
            }, $"ConcurrentOperation-{taskIndex}"));
        }

        var results = await Task.WhenAll(tasks);

        // Assert
        results.Should().HaveCount(3);
        results.Should().OnlyContain(r => r.StartsWith("result-"));
    }

    [TestTimeout(TestTimeouts.Unit)]
    [Trait("Category", TestCategories.Network)]
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _helper.Dispose();
        action.Should().NotThrow();
    }
}
