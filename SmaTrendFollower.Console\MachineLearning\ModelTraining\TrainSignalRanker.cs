using System.Globalization;
using CsvHelper;
using Microsoft.ML;
using Microsoft.ML.AutoML;
using Microsoft.ML.Data;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.MachineLearning.ModelTraining;

/// <summary>
/// Console application for training ML signal ranking models using AutoML.
/// Can be invoked via CLI for automated model training and retraining.
/// </summary>
public static class TrainSignalRanker
{
    private static readonly ILoggerFactory LoggerFactory = Microsoft.Extensions.Logging.LoggerFactory.Create(builder =>
        builder.AddConsole().SetMinimumLevel(LogLevel.Information));
    
    private static readonly ILogger Logger = LoggerFactory.CreateLogger("TrainSignalRanker");

    /// <summary>
    /// Main entry point for signal ranker training
    /// Usage: TrainSignalRanker <csv_path> [model_output_path] [experiment_time_seconds]
    /// </summary>
    // Commented out to avoid multiple entry point warnings
    /*
    public static async Task<int> Main(string[] args)
    {
        try
        {
            if (args.Length == 0)
            {
                System.Console.WriteLine("Usage: TrainSignalRanker <csv_path> [model_output_path] [experiment_time_seconds]");
                System.Console.WriteLine("Example: TrainSignalRanker signals.csv Model/signal_model.zip 300");
                return 1;
            }

            var csvPath = args[0];
            var modelPath = args.Length > 1 ? args[1] : "Model/signal_model.zip";
            var experimentTimeSeconds = args.Length > 2 ? int.Parse(args[2]) : 180;

            if (!File.Exists(csvPath))
            {
                Logger.LogError("CSV file not found: {CsvPath}", csvPath);
                return 1;
            }

            Logger.LogInformation("Starting ML model training...");
            Logger.LogInformation("CSV Path: {CsvPath}", csvPath);
            Logger.LogInformation("Model Output: {ModelPath}", modelPath);
            Logger.LogInformation("Experiment Time: {ExperimentTime}s", experimentTimeSeconds);

            var result = await TrainModelAsync(csvPath, modelPath, experimentTimeSeconds);
            
            if (result.Success)
            {
                Logger.LogInformation("✅ Model training completed successfully!");
                Logger.LogInformation("Model saved to: {ModelPath}", modelPath);
                Logger.LogInformation("Best Model: {BestModel}", result.BestModelName);
                Logger.LogInformation("Accuracy: {Accuracy:P2}", result.Accuracy);
                Logger.LogInformation("AUC: {Auc:F3}", result.Auc);
                Logger.LogInformation("F1 Score: {F1Score:F3}", result.F1Score);
                Logger.LogInformation("Training Samples: {TrainingSamples}", result.TrainingSamples);
                Logger.LogInformation("Validation Samples: {ValidationSamples}", result.ValidationSamples);
                
                return 0;
            }
            else
            {
                Logger.LogError("❌ Model training failed: {Error}", result.ErrorMessage);
                return 1;
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Fatal error during model training");
            return 1;
        }
    }
    */

    /// <summary>
    /// Trains a binary classification model using AutoML
    /// </summary>
    public static async Task<TrainingResult> TrainModelAsync(
        string csvPath, 
        string modelPath, 
        int experimentTimeSeconds = 180)
    {
        try
        {
            var mlContext = new MLContext(seed: 42);
            
            // Load and validate data
            Logger.LogInformation("Loading training data from {CsvPath}...", csvPath);
            var dataView = mlContext.Data.LoadFromTextFile<MLSignalInput>(
                csvPath, 
                hasHeader: true, 
                separatorChar: ',');

            var dataCount = (int)dataView.GetRowCount();
            Logger.LogInformation("Loaded {DataCount} training samples", dataCount);

            if (dataCount < 100)
            {
                return new TrainingResult(false, "Insufficient training data (minimum 100 samples required)", 
                    string.Empty, 0, 0, 0, 0, 0);
            }

            // Split data for training and validation
            var trainTestSplit = mlContext.Data.TrainTestSplit(dataView, testFraction: 0.2, seed: 42);
            var trainData = trainTestSplit.TrainSet;
            var testData = trainTestSplit.TestSet;

            var trainCount = (int)trainData.GetRowCount();
            var testCount = (int)testData.GetRowCount();
            
            Logger.LogInformation("Training samples: {TrainCount}, Validation samples: {TestCount}", 
                trainCount, testCount);

            // Configure AutoML experiment
            Logger.LogInformation("Configuring AutoML experiment (max {ExperimentTime}s)...", experimentTimeSeconds);
            
            var experiment = mlContext.Auto()
                .CreateBinaryClassificationExperiment(maxExperimentTimeInSeconds: (uint)experimentTimeSeconds);

            // Configure experiment settings (AutoML handles dataset internally)

            // Run the experiment
            Logger.LogInformation("🚀 Starting AutoML experiment...");
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            
            var experimentResult = await Task.Run(() => experiment.Execute(trainData, labelColumnName: nameof(MLSignalInput.Win)));
            
            stopwatch.Stop();
            Logger.LogInformation("AutoML experiment completed in {ElapsedTime:F1}s", stopwatch.Elapsed.TotalSeconds);

            // Get the best model
            var bestModel = experimentResult.BestRun.Model;
            var bestModelName = experimentResult.BestRun.TrainerName;
            var metrics = experimentResult.BestRun.ValidationMetrics;

            Logger.LogInformation("Best model: {BestModel}", bestModelName);
            Logger.LogInformation("Validation metrics:");
            Logger.LogInformation("  Accuracy: {Accuracy:P2}", metrics.Accuracy);
            Logger.LogInformation("  AUC: {Auc:F3}", metrics.AreaUnderRocCurve);
            Logger.LogInformation("  F1 Score: {F1Score:F3}", metrics.F1Score);
            Logger.LogInformation("  Precision: {Precision:F3}", metrics.PositivePrecision);
            Logger.LogInformation("  Recall: {Recall:F3}", metrics.PositiveRecall);

            // Ensure output directory exists
            var modelDirectory = Path.GetDirectoryName(modelPath);
            if (!string.IsNullOrEmpty(modelDirectory) && !Directory.Exists(modelDirectory))
            {
                Directory.CreateDirectory(modelDirectory);
                Logger.LogInformation("Created model directory: {ModelDirectory}", modelDirectory);
            }

            // Save the model
            Logger.LogInformation("Saving model to {ModelPath}...", modelPath);
            mlContext.Model.Save(bestModel, dataView.Schema, modelPath);

            // Save model metadata
            var metadataPath = Path.ChangeExtension(modelPath, ".metadata.json");
            var metadata = new
            {
                TrainedAt = DateTime.UtcNow,
                ModelName = bestModelName,
                ExperimentTimeSeconds = experimentTimeSeconds,
                TrainingSamples = trainCount,
                ValidationSamples = testCount,
                Metrics = new
                {
                    Accuracy = metrics.Accuracy,
                    Auc = metrics.AreaUnderRocCurve,
                    F1Score = metrics.F1Score,
                    Precision = metrics.PositivePrecision,
                    Recall = metrics.PositiveRecall,
                    ConfusionMatrix = new
                    {
                        TruePositive = metrics.ConfusionMatrix.Counts[1][1],
                        TrueNegative = metrics.ConfusionMatrix.Counts[0][0],
                        FalsePositive = metrics.ConfusionMatrix.Counts[0][1],
                        FalseNegative = metrics.ConfusionMatrix.Counts[1][0]
                    }
                },
                DataSource = csvPath,
                ModelVersion = "1.0"
            };

            await File.WriteAllTextAsync(metadataPath, 
                System.Text.Json.JsonSerializer.Serialize(metadata, new System.Text.Json.JsonSerializerOptions 
                { 
                    WriteIndented = true 
                }));

            Logger.LogInformation("Model metadata saved to {MetadataPath}", metadataPath);

            return new TrainingResult(
                true, 
                string.Empty, 
                bestModelName, 
                metrics.Accuracy, 
                metrics.AreaUnderRocCurve, 
                metrics.F1Score, 
                trainCount, 
                testCount);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during model training");
            return new TrainingResult(false, ex.Message, string.Empty, 0, 0, 0, 0, 0);
        }
    }

    /// <summary>
    /// Validates the CSV data format and content
    /// </summary>
    public static async Task<(bool IsValid, string ErrorMessage, int RecordCount)> ValidateCsvAsync(string csvPath)
    {
        try
        {
            if (!File.Exists(csvPath))
            {
                return (false, "CSV file not found", 0);
            }

            using var reader = new StreamReader(csvPath);
            using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
            
            var records = csv.GetRecords<MLSignalInput>().ToList();
            
            if (!records.Any())
            {
                return (false, "CSV file is empty", 0);
            }

            // Check for required columns and valid data
            var invalidRecords = records.Where(r => 
                float.IsNaN(r.SmaGap) || float.IsInfinity(r.SmaGap) ||
                float.IsNaN(r.Volatility) || float.IsInfinity(r.Volatility) ||
                r.Rsi < 0 || r.Rsi > 100 ||
                r.VixLevel <= 0).Count();

            if (invalidRecords > 0)
            {
                return (false, $"{invalidRecords} records contain invalid data", records.Count);
            }

            return (true, string.Empty, records.Count);
        }
        catch (Exception ex)
        {
            return (false, $"Error validating CSV: {ex.Message}", 0);
        }
    }
}

/// <summary>
/// Result of model training operation
/// </summary>
public record TrainingResult(
    bool Success,
    string ErrorMessage,
    string BestModelName,
    double Accuracy,
    double Auc,
    double F1Score,
    int TrainingSamples,
    int ValidationSamples
);
